@echo off
chcp 65001 >nul
echo ========================================
echo 低代码平台后端服务器启动脚本
echo ========================================
echo.

echo 1. 检查当前目录...
cd /d "%~dp0"
echo 当前目录: %CD%

echo.
echo 2. 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo ❌ Java未安装或未配置环境变量
    pause
    exit /b 1
)

echo.
echo 3. 检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo ❌ Maven未安装或未配置环境变量
    pause
    exit /b 1
)

echo.
echo 4. 检查端口8080是否被占用...
netstat -an | findstr :8080
if %errorlevel% equ 0 (
    echo ⚠️  端口8080已被占用，请先关闭占用进程
    echo 或者修改application.properties中的server.port配置
    pause
)

echo.
echo 5. 检查字体目录...
if not exist "uploads\font" (
    echo 📁 创建字体目录...
    mkdir uploads\font
)
echo ✅ 字体目录存在: uploads\font

echo.
echo 6. 启动Spring Boot应用...
echo 正在启动服务器，请稍候...
echo.

mvn spring-boot:run

pause
