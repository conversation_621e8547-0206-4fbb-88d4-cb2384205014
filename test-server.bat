@echo off
chcp 65001 >nul
echo ========================================
echo 低代码平台服务器状态检测
echo ========================================
echo.

echo 1. 检查端口8080状态...
netstat -an | findstr :8080
if %errorlevel% neq 0 (
    echo ❌ 端口8080未监听，服务器可能未启动
    echo.
    echo 请先运行 start-server.bat 启动服务器
    pause
    exit /b 1
) else (
    echo ✅ 端口8080正在监听
)

echo.
echo 2. 测试基本API连接...
curl -s -o nul -w "HTTP状态码: %%{http_code}\n" http://localhost:8080/api/fonts/debug/file-system
if %errorlevel% neq 0 (
    echo ❌ 无法连接到服务器API
) else (
    echo ✅ API连接正常
)

echo.
echo 3. 测试字体文件访问...
curl -s -o nul -w "HTTP状态码: %%{http_code}\n" http://localhost:8080/api/fonts/file/font_20250602032451_bc753e43.TTF
if %errorlevel% neq 0 (
    echo ❌ 字体文件访问失败
) else (
    echo ✅ 字体文件访问正常
)

echo.
echo 4. 测试CORS头部...
echo 检查CORS响应头:
curl -I -H "Origin: http://localhost:3000" http://localhost:8080/api/fonts/file/font_20250602032451_bc753e43.TTF 2>nul | findstr "Access-Control"

echo.
echo 5. 显示字体目录状态...
curl -s http://localhost:8080/api/fonts/debug/file-system 2>nul

echo.
echo ========================================
echo 测试完成
echo ========================================
echo.
echo 如果所有测试都通过，您可以在前端重新测试字体加载功能
echo 如果有测试失败，请检查对应的问题并重新启动服务器
echo.

pause
