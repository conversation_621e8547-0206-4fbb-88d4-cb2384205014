<template>
  <div class="input-props-editor-test">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>InputPropsEditor 测试页面</span>
          <el-button type="primary" @click="refreshTest">刷新测试</el-button>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="test-card">
            <template #header>
              <h3>🧪 组件测试</h3>
            </template>
            
            <div class="test-content">
              <el-form label-width="120px">
                <el-form-item label="测试场景:">
                  <el-select v-model="testScenario" @change="updateTestComponent">
                    <el-option label="正常组件" value="normal" />
                    <el-option label="空组件" value="empty" />
                    <el-option label="无属性组件" value="no-props" />
                    <el-option label="部分属性组件" value="partial-props" />
                    <el-option label="null组件" value="null" />
                  </el-select>
                </el-form-item>
              </el-form>
              
              <el-divider />
              
              <div class="component-test">
                <h4>InputPropsEditor 渲染测试:</h4>
                <div class="editor-wrapper">
                  <input-props-editor
                    v-if="testComponent"
                    :component="testComponent"
                    @update="handleComponentUpdate"
                  />
                  <div v-else class="no-component">
                    <el-alert
                      title="组件为空"
                      description="当前测试组件为 null"
                      type="warning"
                      show-icon
                    />
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card class="test-card">
            <template #header>
              <h3>📊 测试结果</h3>
            </template>
            
            <div class="test-results">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="测试场景">
                  {{ getScenarioName(testScenario) }}
                </el-descriptions-item>
                <el-descriptions-item label="组件状态">
                  <el-tag :type="testComponent ? 'success' : 'danger'">
                    {{ testComponent ? '有效' : '无效' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="渲染状态">
                  <el-tag :type="renderError ? 'danger' : 'success'">
                    {{ renderError ? '渲染错误' : '渲染正常' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="错误信息">
                  {{ renderError || '无错误' }}
                </el-descriptions-item>
              </el-descriptions>
              
              <el-divider />
              
              <div class="component-data">
                <h4>组件数据:</h4>
                <pre>{{ JSON.stringify(testComponent, null, 2) }}</pre>
              </div>
              
              <el-divider />
              
              <div class="update-log">
                <h4>更新日志:</h4>
                <div v-if="updateLogs.length === 0" class="no-logs">
                  <el-empty description="暂无更新记录" />
                </div>
                <div v-else class="log-list">
                  <div
                    v-for="(log, index) in updateLogs.slice(-5)"
                    :key="index"
                    class="log-item"
                  >
                    <el-tag size="small">{{ log.timestamp }}</el-tag>
                    <span class="log-message">{{ log.message }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onErrorCaptured } from 'vue'
import { ElMessage } from 'element-plus'
import InputPropsEditor from '../components/editor/props/InputPropsEditor.vue'

// 响应式数据
const testScenario = ref('normal')
const testComponent = ref(null)
const renderError = ref('')
const updateLogs = ref([])

// 测试场景配置
const scenarios = {
  normal: {
    name: '正常组件',
    component: {
      id: 'test-input-1',
      type: 'input',
      props: {
        placeholder: '请输入内容',
        defaultValue: '',
        maxLength: 100,
        type: 'text',
        clearable: true,
        showPassword: false,
        disabled: false,
        readonly: false,
        prefixIcon: '',
        suffixIcon: '',
        dataKey: 'testInput',
        validation: {
          rules: [
            { type: 'required', message: '此字段为必填项' }
          ]
        }
      },
      styles: {}
    }
  },
  empty: {
    name: '空组件',
    component: {
      id: 'test-input-2',
      type: 'input',
      props: {},
      styles: {}
    }
  },
  'no-props': {
    name: '无属性组件',
    component: {
      id: 'test-input-3',
      type: 'input',
      styles: {}
    }
  },
  'partial-props': {
    name: '部分属性组件',
    component: {
      id: 'test-input-4',
      type: 'input',
      props: {
        placeholder: '部分属性测试',
        dataKey: 'partialTest'
      },
      styles: {}
    }
  },
  null: {
    name: 'null组件',
    component: null
  }
}

// 方法
const updateTestComponent = () => {
  const scenario = scenarios[testScenario.value]
  testComponent.value = scenario.component
  renderError.value = ''
  
  addUpdateLog(`切换到测试场景: ${scenario.name}`)
}

const handleComponentUpdate = (updatedComponent) => {
  testComponent.value = updatedComponent
  addUpdateLog(`组件已更新: ${JSON.stringify(updatedComponent.props, null, 2)}`)
  ElMessage.success('组件更新成功')
}

const addUpdateLog = (message) => {
  updateLogs.value.push({
    timestamp: new Date().toLocaleTimeString(),
    message
  })
  
  // 保持日志数量在合理范围内
  if (updateLogs.value.length > 20) {
    updateLogs.value.shift()
  }
}

const getScenarioName = (scenario) => {
  return scenarios[scenario]?.name || '未知场景'
}

const refreshTest = () => {
  updateTestComponent()
  ElMessage.info('测试已刷新')
}

// 错误捕获
onErrorCaptured((error, instance, info) => {
  renderError.value = error.message
  addUpdateLog(`渲染错误: ${error.message}`)
  console.error('InputPropsEditor Test Error:', error, instance, info)
  return false // 阻止错误继续传播
})

// 生命周期
onMounted(() => {
  updateTestComponent()
  addUpdateLog('测试页面已加载')
})
</script>

<style scoped>
.input-props-editor-test {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-card {
  height: 100%;
}

.test-content {
  padding: 10px 0;
}

.editor-wrapper {
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  min-height: 200px;
  background-color: #fafafa;
}

.no-component {
  text-align: center;
  padding: 20px;
}

.test-results {
  padding: 10px 0;
}

.component-data {
  margin-top: 20px;
}

.component-data pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}

.update-log {
  margin-top: 20px;
}

.log-list {
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #ebeef5;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-message {
  flex: 1;
  color: #606266;
}

.no-logs {
  text-align: center;
  padding: 20px;
}
</style>
