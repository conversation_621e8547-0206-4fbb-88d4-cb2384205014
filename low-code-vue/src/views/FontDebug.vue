<template>
  <div class="font-debug-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>字体数据调试页面</span>
          <el-button type="primary" @click="loadFonts">重新加载字体</el-button>
        </div>
      </template>

      <!-- API测试 -->
      <el-card class="debug-section">
        <template #header>
          <h3>1. API测试</h3>
        </template>
        
        <div class="debug-content">
          <el-button @click="testGetEnabledFonts">测试 getEnabledFonts API</el-button>
          <el-button @click="testDebugFileSystem">测试文件系统调试 API</el-button>
          
          <div v-if="apiResult" class="api-result">
            <h4>API返回结果:</h4>
            <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
          </div>
        </div>
      </el-card>

      <!-- Store状态 -->
      <el-card class="debug-section">
        <template #header>
          <h3>2. Store状态</h3>
        </template>
        
        <div class="debug-content">
          <el-button @click="refreshStoreInfo">刷新Store信息</el-button>
          
          <div class="store-info">
            <h4>字体库统计:</h4>
            <ul>
              <li>总字体数量: {{ fontLibrary.length }}</li>
              <li>系统字体: {{ systemFonts.length }}</li>
              <li>Web字体: {{ webFonts.length }}</li>
              <li>自定义字体: {{ customFonts.length }}</li>
            </ul>
            
            <h4>自定义字体详情:</h4>
            <el-table :data="customFonts" style="width: 100%" size="small">
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="name" label="名称" width="150" />
              <el-table-column prop="family" label="字体族" />
              <el-table-column prop="type" label="类型" width="80" />
              <el-table-column prop="fontUrl" label="字体URL" />
            </el-table>
          </div>
        </div>
      </el-card>

      <!-- 网络请求测试 -->
      <el-card class="debug-section">
        <template #header>
          <h3>3. 网络请求测试</h3>
        </template>
        
        <div class="debug-content">
          <el-form inline>
            <el-form-item label="测试URL:">
              <el-input v-model="testUrl" style="width: 400px" />
            </el-form-item>
            <el-form-item>
              <el-button @click="testUrl">测试访问</el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="networkResult" class="network-result">
            <h4>网络测试结果:</h4>
            <pre>{{ JSON.stringify(networkResult, null, 2) }}</pre>
          </div>
        </div>
      </el-card>

      <!-- 日志输出 -->
      <el-card class="debug-section">
        <template #header>
          <h3>4. 调试日志</h3>
        </template>
        
        <div class="debug-content">
          <el-button @click="clearLogs">清除日志</el-button>
          
          <div class="log-container">
            <div v-for="(log, index) in debugLogs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span :class="['log-level', `log-${log.level}`]">{{ log.level.toUpperCase() }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useEditorStore } from '../store'
import { fontApi } from '../api/font'

const editorStore = useEditorStore()

// 响应式数据
const apiResult = ref(null)
const networkResult = ref(null)
const testUrl = ref('http://localhost:8080/api/fonts/enabled')
const debugLogs = ref([])

// 计算属性
const fontLibrary = computed(() => editorStore.fontLibrary)
const systemFonts = computed(() => fontLibrary.value.filter(font => font.type === 'system'))
const webFonts = computed(() => fontLibrary.value.filter(font => font.type === 'web'))
const customFonts = computed(() => fontLibrary.value.filter(font => font.type === 'custom'))

// 添加日志
const addLog = (level, message) => {
  debugLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    level,
    message
  })
  
  // 限制日志数量
  if (debugLogs.value.length > 100) {
    debugLogs.value = debugLogs.value.slice(0, 100)
  }
}

// 清除日志
const clearLogs = () => {
  debugLogs.value = []
}

// 测试 getEnabledFonts API
const testGetEnabledFonts = async () => {
  try {
    addLog('info', '开始测试 getEnabledFonts API')
    
    const result = await fontApi.getEnabledFonts()
    apiResult.value = result
    
    addLog('success', `API调用成功，返回数据类型: ${typeof result}`)
    
    if (result && result.data) {
      addLog('info', `响应包含data字段，数据类型: ${typeof result.data}，长度: ${Array.isArray(result.data) ? result.data.length : 'N/A'}`)
    } else if (Array.isArray(result)) {
      addLog('info', `直接返回数组，长度: ${result.length}`)
    } else {
      addLog('warning', '返回数据格式不符合预期')
    }
    
    ElMessage.success('API测试完成')
  } catch (error) {
    addLog('error', `API调用失败: ${error.message}`)
    apiResult.value = { error: error.message }
    ElMessage.error('API测试失败')
  }
}

// 测试文件系统调试 API
const testDebugFileSystem = async () => {
  try {
    addLog('info', '开始测试文件系统调试 API')
    
    const result = await fontApi.debugFileSystem()
    apiResult.value = result
    
    addLog('success', '文件系统调试API调用成功')
    ElMessage.success('文件系统调试完成')
  } catch (error) {
    addLog('error', `文件系统调试失败: ${error.message}`)
    apiResult.value = { error: error.message }
    ElMessage.error('文件系统调试失败')
  }
}

// 测试网络访问
const testNetworkAccess = async () => {
  try {
    addLog('info', `测试网络访问: ${testUrl.value}`)
    
    const response = await fetch(testUrl.value, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    })
    
    const data = await response.json()
    
    networkResult.value = {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      data: data
    }
    
    addLog('success', `网络访问成功: ${response.status}`)
    ElMessage.success('网络测试完成')
  } catch (error) {
    addLog('error', `网络访问失败: ${error.message}`)
    networkResult.value = { error: error.message }
    ElMessage.error('网络测试失败')
  }
}

// 加载字体
const loadFonts = async () => {
  try {
    addLog('info', '开始重新加载字体')
    
    const result = await fontApi.getEnabledFonts()
    
    if (result && result.data && Array.isArray(result.data)) {
      editorStore.setFontLibrary(result.data)
      addLog('success', `字体加载成功: ${result.data.length} 个字体`)
    } else if (result && Array.isArray(result)) {
      editorStore.setFontLibrary(result)
      addLog('success', `字体加载成功: ${result.length} 个字体`)
    } else {
      addLog('warning', '字体数据格式不正确')
    }
    
    ElMessage.success('字体重新加载完成')
  } catch (error) {
    addLog('error', `字体加载失败: ${error.message}`)
    ElMessage.error('字体加载失败')
  }
}

// 刷新Store信息
const refreshStoreInfo = () => {
  addLog('info', `Store信息已刷新 - 总字体: ${fontLibrary.value.length}`)
}

// 组件挂载时初始化
onMounted(() => {
  addLog('info', '字体调试页面已加载')
  refreshStoreInfo()
})
</script>

<style scoped>
.font-debug-page {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.debug-section {
  margin-bottom: 20px;
}

.debug-content {
  padding: 10px 0;
}

.api-result,
.network-result {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.api-result pre,
.network-result pre {
  margin: 0;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
}

.store-info {
  margin-top: 15px;
}

.store-info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  background-color: #fafafa;
  margin-top: 10px;
}

.log-item {
  display: flex;
  gap: 10px;
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #909399;
  min-width: 80px;
}

.log-level {
  min-width: 60px;
  font-weight: bold;
}

.log-success {
  color: #67c23a;
}

.log-error {
  color: #f56c6c;
}

.log-warning {
  color: #e6a23c;
}

.log-info {
  color: #409eff;
}

.log-message {
  flex: 1;
}
</style>
