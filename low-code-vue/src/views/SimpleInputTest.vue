<template>
  <div class="simple-input-test">
    <el-card>
      <template #header>
        <h3>简单输入框测试</h3>
      </template>
      
      <div class="test-content">
        <el-button @click="createTestComponent">创建测试组件</el-button>
        <el-button @click="clearTestComponent">清空组件</el-button>
        <el-button @click="createEmptyComponent">创建空组件</el-button>
        
        <el-divider />
        
        <div class="component-info">
          <h4>当前组件信息:</h4>
          <pre>{{ JSON.stringify(testComponent, null, 2) }}</pre>
        </div>
        
        <el-divider />
        
        <div class="editor-test">
          <h4>InputPropsEditor 测试:</h4>
          <div v-if="testComponent" class="editor-wrapper">
            <input-props-editor
              :component="testComponent"
              @update="handleUpdate"
            />
          </div>
          <div v-else class="no-component">
            <p>没有组件</p>
          </div>
        </div>
        
        <el-divider />
        
        <div class="error-info">
          <h4>错误信息:</h4>
          <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>
          <div v-else class="no-error">
            无错误
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onErrorCaptured } from 'vue'
import { ElMessage } from 'element-plus'
import InputPropsEditor from '../components/editor/props/InputPropsEditor.vue'

const testComponent = ref(null)
const errorMessage = ref('')

const createTestComponent = () => {
  testComponent.value = {
    id: 'test-input-' + Date.now(),
    type: 'input',
    props: {
      placeholder: '测试占位符',
      dataKey: 'testKey',
      type: 'text',
      clearable: true
    },
    styles: {}
  }
  errorMessage.value = ''
  ElMessage.success('测试组件已创建')
}

const clearTestComponent = () => {
  testComponent.value = null
  errorMessage.value = ''
  ElMessage.info('组件已清空')
}

const createEmptyComponent = () => {
  testComponent.value = {
    id: 'empty-input-' + Date.now(),
    type: 'input',
    props: {},
    styles: {}
  }
  errorMessage.value = ''
  ElMessage.info('空组件已创建')
}

const handleUpdate = (updatedComponent) => {
  testComponent.value = updatedComponent
  ElMessage.success('组件已更新')
}

// 错误捕获
onErrorCaptured((error, instance, info) => {
  errorMessage.value = `${error.message} (${info})`
  console.error('Simple Input Test Error:', error, instance, info)
  return false
})
</script>

<style scoped>
.simple-input-test {
  padding: 20px;
}

.test-content {
  padding: 20px;
}

.component-info pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.editor-wrapper {
  border: 1px solid #ddd;
  padding: 15px;
  border-radius: 4px;
  background-color: #fafafa;
}

.no-component {
  text-align: center;
  padding: 20px;
  color: #999;
}

.error-message {
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #fbc4c4;
}

.no-error {
  color: #67c23a;
  background-color: #f0f9ff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
}
</style>
