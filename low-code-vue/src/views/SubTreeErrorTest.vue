<template>
  <div class="subtree-error-test">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>SubTree 错误测试页面</span>
          <el-button type="primary" @click="refreshTest">刷新测试</el-button>
        </div>
      </template>

      <!-- 测试场景 -->
      <div class="test-scenarios">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="test-card">
              <template #header>
                <h3>🧪 动态组件测试</h3>
              </template>
              
              <div class="test-content">
                <el-form label-width="120px">
                  <el-form-item label="组件类型:">
                    <el-select v-model="testComponentType" @change="updateTestComponent">
                      <el-option label="文本组件" value="text" />
                      <el-option label="按钮组件" value="button" />
                      <el-option label="输入框组件" value="input" />
                      <el-option label="无效组件" value="invalid" />
                      <el-option label="空组件" value="" />
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item label="强制错误:">
                    <el-switch v-model="forceError" @change="updateTestComponent" />
                  </el-form-item>
                </el-form>
                
                <el-divider />
                
                <div class="dynamic-component-test">
                  <h4>动态组件渲染测试:</h4>
                  <div class="component-wrapper">
                    <!-- 使用修复后的 ComponentPropsEditor -->
                    <component-props-editor
                      v-if="testComponent && !forceError"
                      :component="testComponent"
                      @update="handleComponentUpdate"
                    />
                    <div v-else-if="forceError" class="error-simulation">
                      <el-alert
                        title="模拟错误状态"
                        description="这里模拟了组件为 null 的情况"
                        type="warning"
                        show-icon
                      />
                    </div>
                    <div v-else class="no-component">
                      <el-empty description="无组件数据" />
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="12">
            <el-card class="test-card">
              <template #header>
                <h3>📊 错误监控</h3>
              </template>
              
              <div class="error-monitoring">
                <el-statistic title="捕获的错误数" :value="capturedErrors" />
                
                <el-divider />
                
                <div class="recent-errors">
                  <h4>最近的错误:</h4>
                  <div v-if="recentErrorLogs.length === 0" class="no-errors">
                    <el-tag type="success">暂无错误 ✅</el-tag>
                  </div>
                  <div v-else class="error-list">
                    <div
                      v-for="(error, index) in recentErrorLogs.slice(0, 3)"
                      :key="index"
                      class="error-item"
                    >
                      <el-tag :type="getErrorTagType(error.type)" size="small">
                        {{ error.type }}
                      </el-tag>
                      <span class="error-message">{{ error.message }}</span>
                      <span class="error-time">{{ formatTime(error.timestamp) }}</span>
                    </div>
                  </div>
                </div>
                
                <el-divider />
                
                <div class="test-actions">
                  <el-button @click="triggerTestError">触发测试错误</el-button>
                  <el-button @click="clearTestErrors">清除错误</el-button>
                  <el-button type="primary" @click="goToErrorDiagnostic">查看详细诊断</el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 组件状态显示 -->
      <el-card class="test-card">
        <template #header>
          <h3>🔍 组件状态详情</h3>
        </template>
        
        <div class="component-details">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="组件类型">
              {{ testComponent?.type || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="组件ID">
              {{ testComponent?.id || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="是否有效">
              <el-tag :type="isValidComponent ? 'success' : 'danger'">
                {{ isValidComponent ? '有效' : '无效' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="错误状态">
              <el-tag :type="forceError ? 'danger' : 'success'">
                {{ forceError ? '强制错误' : '正常' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
          
          <el-divider />
          
          <div class="component-json">
            <h4>组件数据 (JSON):</h4>
            <pre>{{ JSON.stringify(testComponent, null, 2) }}</pre>
          </div>
        </div>
      </el-card>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import ComponentPropsEditor from '../components/editor/ComponentPropsEditor.vue'
import { 
  getErrorLogs, 
  clearErrorLogs, 
  logError, 
  ERROR_TYPES,
  isSafeToRender 
} from '../utils/vueErrorHandler'

const router = useRouter()

// 响应式数据
const testComponentType = ref('text')
const forceError = ref(false)
const testComponent = ref(null)
const recentErrorLogs = ref([])

// 计算属性
const capturedErrors = computed(() => recentErrorLogs.value.length)

const isValidComponent = computed(() => {
  return testComponent.value && isSafeToRender(testComponent.value)
})

// 方法
const updateTestComponent = () => {
  if (forceError.value) {
    testComponent.value = null
    return
  }
  
  if (!testComponentType.value) {
    testComponent.value = null
    return
  }
  
  // 创建测试组件
  testComponent.value = {
    id: `test-${testComponentType.value}-${Date.now()}`,
    type: testComponentType.value,
    props: {
      text: '测试文本',
      placeholder: '测试占位符'
    },
    styles: {
      color: '#333',
      fontSize: '14px'
    }
  }
}

const handleComponentUpdate = (updatedComponent) => {
  console.log('组件更新:', updatedComponent)
  testComponent.value = updatedComponent
  ElMessage.success('组件更新成功')
}

const refreshTest = () => {
  updateTestComponent()
  refreshErrorLogs()
  ElMessage.info('测试已刷新')
}

const refreshErrorLogs = () => {
  recentErrorLogs.value = getErrorLogs()
}

const triggerTestError = () => {
  // 故意触发一个错误来测试错误处理
  logError(ERROR_TYPES.SUBTREE_NULL, '这是一个测试错误', {
    testComponent: testComponent.value,
    timestamp: new Date().toISOString()
  })
  
  refreshErrorLogs()
  ElMessage.warning('已触发测试错误')
}

const clearTestErrors = () => {
  clearErrorLogs()
  recentErrorLogs.value = []
  ElMessage.success('错误已清除')
}

const goToErrorDiagnostic = () => {
  router.push('/vue-error-diagnostic')
}

const getErrorTagType = (errorType) => {
  switch (errorType) {
    case ERROR_TYPES.SUBTREE_NULL:
      return 'danger'
    case ERROR_TYPES.COMPONENT_NULL:
      return 'warning'
    case ERROR_TYPES.RENDER_ERROR:
      return 'danger'
    default:
      return 'info'
  }
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 监听器
watch(() => testComponentType.value, () => {
  if (!forceError.value) {
    updateTestComponent()
  }
})

// 定期刷新错误日志
let errorRefreshTimer = null

// 生命周期
onMounted(() => {
  updateTestComponent()
  refreshErrorLogs()
  
  // 每3秒刷新一次错误日志
  errorRefreshTimer = setInterval(refreshErrorLogs, 3000)
})

// 清理定时器
onUnmounted(() => {
  if (errorRefreshTimer) {
    clearInterval(errorRefreshTimer)
  }
})
</script>

<style scoped>
.subtree-error-test {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-scenarios {
  margin-bottom: 20px;
}

.test-card {
  margin-bottom: 20px;
  height: 100%;
}

.test-content {
  padding: 10px 0;
}

.component-wrapper {
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  min-height: 100px;
  background-color: #fafafa;
}

.error-simulation {
  text-align: center;
}

.no-component {
  text-align: center;
}

.error-monitoring {
  padding: 10px 0;
}

.error-list {
  max-height: 200px;
  overflow-y: auto;
}

.error-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
  font-size: 12px;
}

.error-item:last-child {
  border-bottom: none;
}

.error-message {
  flex: 1;
  color: #606266;
}

.error-time {
  color: #909399;
  font-size: 11px;
}

.no-errors {
  text-align: center;
  padding: 20px;
}

.test-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.component-details {
  padding: 10px 0;
}

.component-json {
  margin-top: 20px;
}

.component-json pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}
</style>
