<template>
  <div class="vue-error-diagnostic">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>Vue.js 错误诊断工具</span>
          <div class="header-actions">
            <el-button type="primary" @click="refreshData">刷新数据</el-button>
            <el-button type="warning" @click="clearAllErrors">清除错误</el-button>
          </div>
        </div>
      </template>

      <!-- 错误统计 -->
      <el-card class="diagnostic-section">
        <template #header>
          <h3>📊 错误统计</h3>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="总错误数" :value="totalErrors" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="SubTree 错误" :value="subTreeErrors" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="组件错误" :value="componentErrors" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="渲染错误" :value="renderErrors" />
          </el-col>
        </el-row>
      </el-card>

      <!-- 错误类型分布 -->
      <el-card class="diagnostic-section">
        <template #header>
          <h3>🔍 错误类型分布</h3>
        </template>
        
        <div class="error-types">
          <el-tag
            v-for="(count, type) in errorTypeStats"
            :key="type"
            :type="getErrorTypeTagType(type)"
            size="large"
            class="error-type-tag"
          >
            {{ type }}: {{ count }}
          </el-tag>
        </div>
      </el-card>

      <!-- 最近错误日志 -->
      <el-card class="diagnostic-section">
        <template #header>
          <div class="section-header">
            <h3>📝 最近错误日志</h3>
            <el-switch
              v-model="autoRefresh"
              active-text="自动刷新"
              inactive-text="手动刷新"
              @change="toggleAutoRefresh"
            />
          </div>
        </template>
        
        <div class="error-logs">
          <div v-if="errorLogs.length === 0" class="no-errors">
            <el-empty description="暂无错误日志" />
          </div>
          <div v-else>
            <div
              v-for="(error, index) in recentErrors"
              :key="index"
              class="error-log-item"
              :class="`error-${error.type.toLowerCase()}`"
            >
              <div class="error-header">
                <el-tag :type="getErrorTypeTagType(error.type)" size="small">
                  {{ error.type }}
                </el-tag>
                <span class="error-time">{{ formatTime(error.timestamp) }}</span>
              </div>
              <div class="error-message">{{ error.message }}</div>
              <div v-if="error.context && Object.keys(error.context).length > 0" class="error-context">
                <el-collapse>
                  <el-collapse-item title="错误上下文" name="context">
                    <pre>{{ JSON.stringify(error.context, null, 2) }}</pre>
                  </el-collapse-item>
                </el-collapse>
              </div>
              <div v-if="showStackTrace && error.stack" class="error-stack">
                <el-collapse>
                  <el-collapse-item title="堆栈跟踪" name="stack">
                    <pre>{{ error.stack }}</pre>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
            
            <div v-if="errorLogs.length > 10" class="load-more">
              <el-button @click="loadMoreErrors">加载更多错误</el-button>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 组件状态诊断 -->
      <el-card class="diagnostic-section">
        <template #header>
          <h3>🔧 组件状态诊断</h3>
        </template>
        
        <div class="component-diagnosis">
          <el-form inline>
            <el-form-item label="组件类型:">
              <el-input
                v-model="testComponentType"
                placeholder="输入组件类型"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="diagnoseComponentType">诊断组件</el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="componentDiagnosis" class="diagnosis-result">
            <h4>诊断结果:</h4>
            <div class="diagnosis-status">
              <el-tag :type="componentDiagnosis.isValid ? 'success' : 'danger'">
                {{ componentDiagnosis.isValid ? '✅ 组件正常' : '❌ 组件异常' }}
              </el-tag>
            </div>
            
            <div v-if="componentDiagnosis.issues.length > 0" class="diagnosis-issues">
              <h5>问题:</h5>
              <ul>
                <li v-for="issue in componentDiagnosis.issues" :key="issue" class="issue-item">
                  {{ issue }}
                </li>
              </ul>
            </div>
            
            <div v-if="componentDiagnosis.warnings.length > 0" class="diagnosis-warnings">
              <h5>警告:</h5>
              <ul>
                <li v-for="warning in componentDiagnosis.warnings" :key="warning" class="warning-item">
                  {{ warning }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 修复建议 -->
      <el-card class="diagnostic-section">
        <template #header>
          <h3>💡 修复建议</h3>
        </template>
        
        <div class="fix-suggestions">
          <el-alert
            v-for="suggestion in fixSuggestions"
            :key="suggestion.type"
            :title="suggestion.title"
            :description="suggestion.description"
            :type="suggestion.alertType"
            show-icon
            class="suggestion-item"
          />
        </div>
      </el-card>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getErrorLogs, 
  clearErrorLogs, 
  ERROR_TYPES, 
  diagnoseComponent 
} from '../utils/vueErrorHandler'

// 响应式数据
const errorLogs = ref([])
const autoRefresh = ref(false)
const showStackTrace = ref(false)
const testComponentType = ref('')
const componentDiagnosis = ref(null)
const displayCount = ref(10)

// 自动刷新定时器
let refreshTimer = null

// 计算属性
const totalErrors = computed(() => errorLogs.value.length)

const subTreeErrors = computed(() => 
  errorLogs.value.filter(error => error.type === ERROR_TYPES.SUBTREE_NULL).length
)

const componentErrors = computed(() => 
  errorLogs.value.filter(error => error.type === ERROR_TYPES.COMPONENT_NULL).length
)

const renderErrors = computed(() => 
  errorLogs.value.filter(error => error.type === ERROR_TYPES.RENDER_ERROR).length
)

const errorTypeStats = computed(() => {
  const stats = {}
  errorLogs.value.forEach(error => {
    stats[error.type] = (stats[error.type] || 0) + 1
  })
  return stats
})

const recentErrors = computed(() => 
  errorLogs.value.slice(0, displayCount.value).reverse()
)

const fixSuggestions = computed(() => {
  const suggestions = []
  
  if (subTreeErrors.value > 0) {
    suggestions.push({
      type: 'subtree',
      title: 'SubTree 错误修复',
      description: '检查动态组件的 :is 属性是否为 null，添加 v-if 条件渲染',
      alertType: 'error'
    })
  }
  
  if (componentErrors.value > 0) {
    suggestions.push({
      type: 'component',
      title: '组件错误修复',
      description: '确保组件对象包含必要的 type 和 id 属性',
      alertType: 'warning'
    })
  }
  
  if (renderErrors.value > 0) {
    suggestions.push({
      type: 'render',
      title: '渲染错误修复',
      description: '检查组件模板语法和数据绑定是否正确',
      alertType: 'info'
    })
  }
  
  return suggestions
})

// 方法
const refreshData = () => {
  errorLogs.value = getErrorLogs()
  ElMessage.success('数据已刷新')
}

const clearAllErrors = () => {
  clearErrorLogs()
  errorLogs.value = []
  ElMessage.success('错误日志已清除')
}

const toggleAutoRefresh = (enabled) => {
  if (enabled) {
    refreshTimer = setInterval(refreshData, 5000) // 每5秒刷新一次
    ElMessage.info('已启用自动刷新')
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
    ElMessage.info('已禁用自动刷新')
  }
}

const loadMoreErrors = () => {
  displayCount.value += 10
}

const diagnoseComponentType = () => {
  if (!testComponentType.value) {
    ElMessage.warning('请输入组件类型')
    return
  }
  
  // 创建测试组件对象
  const testComponent = {
    type: testComponentType.value,
    id: 'test-component',
    props: {},
    styles: {}
  }
  
  componentDiagnosis.value = diagnoseComponent(testComponent)
  ElMessage.success('组件诊断完成')
}

const getErrorTypeTagType = (errorType) => {
  switch (errorType) {
    case ERROR_TYPES.SUBTREE_NULL:
      return 'danger'
    case ERROR_TYPES.COMPONENT_NULL:
      return 'warning'
    case ERROR_TYPES.RENDER_ERROR:
      return 'danger'
    case ERROR_TYPES.LIFECYCLE_ERROR:
      return 'warning'
    case ERROR_TYPES.DYNAMIC_COMPONENT_ERROR:
      return 'info'
    default:
      return 'info'
  }
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString()
}

// 生命周期
onMounted(() => {
  refreshData()
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.vue-error-diagnostic {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.diagnostic-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-types {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.error-type-tag {
  margin: 5px;
}

.error-logs {
  max-height: 600px;
  overflow-y: auto;
}

.error-log-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
  background-color: #fafafa;
}

.error-log-item.error-subtree_null {
  border-left: 4px solid #f56c6c;
}

.error-log-item.error-component_null {
  border-left: 4px solid #e6a23c;
}

.error-log-item.error-render_error {
  border-left: 4px solid #f56c6c;
}

.error-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.error-time {
  font-size: 12px;
  color: #909399;
}

.error-message {
  font-weight: 500;
  margin-bottom: 10px;
}

.error-context,
.error-stack {
  margin-top: 10px;
}

.error-context pre,
.error-stack pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

.load-more {
  text-align: center;
  margin-top: 20px;
}

.component-diagnosis {
  margin-top: 20px;
}

.diagnosis-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.diagnosis-status {
  margin-bottom: 15px;
}

.diagnosis-issues,
.diagnosis-warnings {
  margin-top: 15px;
}

.issue-item {
  color: #f56c6c;
  margin-bottom: 5px;
}

.warning-item {
  color: #e6a23c;
  margin-bottom: 5px;
}

.fix-suggestions {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.suggestion-item {
  margin-bottom: 0;
}

.no-errors {
  text-align: center;
  padding: 40px;
}
</style>
