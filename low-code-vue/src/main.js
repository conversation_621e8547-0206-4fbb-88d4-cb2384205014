import './assets/main.css'

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { globalErrorHandler } from './utils/vueErrorHandler'

const app = createApp(App)

// Register Element Plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 设置全局错误处理器
app.config.errorHandler = globalErrorHandler

// 设置全局警告处理器（开发环境）
if (process.env.NODE_ENV === 'development') {
  app.config.warnHandler = (msg, instance, trace) => {
    console.warn(`[Vue warn]: ${msg}`, { instance, trace })
  }
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

app.mount('#app')