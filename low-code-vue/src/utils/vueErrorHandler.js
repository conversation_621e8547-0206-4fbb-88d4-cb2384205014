/**
 * Vue.js 错误处理工具
 * 专门用于处理 subTree 相关错误和组件生命周期问题
 */
import { h } from 'vue'

// 错误类型枚举
export const ERROR_TYPES = {
  SUBTREE_NULL: 'SUBTREE_NULL',
  COMPONENT_NULL: 'COMPONENT_NULL',
  LIFECYCLE_ERROR: 'LIFECYCLE_ERROR',
  DYNAMIC_COMPONENT_ERROR: 'DYNAMIC_COMPONENT_ERROR',
  RENDER_ERROR: 'RENDER_ERROR'
}

// 错误日志存储
const errorLogs = []

/**
 * 添加错误日志
 * @param {string} type 错误类型
 * @param {string} message 错误消息
 * @param {Object} context 错误上下文
 */
export function logError(type, message, context = {}) {
  const errorLog = {
    type,
    message,
    context,
    timestamp: new Date().toISOString(),
    stack: new Error().stack
  }
  
  errorLogs.push(errorLog)
  console.error(`[Vue Error Handler] ${type}: ${message}`, context)
  
  // 保持日志数量在合理范围内
  if (errorLogs.length > 100) {
    errorLogs.shift()
  }
}

/**
 * 获取错误日志
 * @returns {Array} 错误日志数组
 */
export function getErrorLogs() {
  return [...errorLogs]
}

/**
 * 清除错误日志
 */
export function clearErrorLogs() {
  errorLogs.length = 0
}

/**
 * 安全的组件渲染检查
 * @param {Object} component 组件对象
 * @returns {boolean} 是否安全渲染
 */
export function isSafeToRender(component) {
  if (!component) {
    logError(ERROR_TYPES.COMPONENT_NULL, 'Component is null or undefined')
    return false
  }
  
  if (!component.type) {
    logError(ERROR_TYPES.COMPONENT_NULL, 'Component type is missing', { component })
    return false
  }
  
  return true
}

/**
 * 安全的动态组件检查
 * @param {*} dynamicComponent 动态组件
 * @param {string} componentType 组件类型
 * @returns {boolean} 是否安全渲染
 */
export function isSafeDynamicComponent(dynamicComponent, componentType) {
  if (!dynamicComponent) {
    logError(ERROR_TYPES.DYNAMIC_COMPONENT_ERROR, `Dynamic component is null for type: ${componentType}`)
    return false
  }
  
  if (typeof dynamicComponent !== 'function' && typeof dynamicComponent !== 'object') {
    logError(ERROR_TYPES.DYNAMIC_COMPONENT_ERROR, `Invalid dynamic component type for: ${componentType}`, { 
      componentType: typeof dynamicComponent 
    })
    return false
  }
  
  return true
}

/**
 * 创建安全的组件包装器
 * @param {*} component 原始组件
 * @param {string} fallbackComponent 后备组件名称
 * @returns {*} 安全的组件
 */
export function createSafeComponent(component, fallbackComponent = 'div') {
  return {
    name: 'SafeComponentWrapper',
    props: ['component'],
    render() {
      try {
        if (!this.component || !isSafeToRender(this.component)) {
          return h(fallbackComponent, { class: 'safe-component-fallback' }, '组件加载失败')
        }
        
        if (!isSafeDynamicComponent(component, this.component.type)) {
          return h(fallbackComponent, { class: 'safe-component-fallback' }, '组件类型不支持')
        }
        
        return h(component, { component: this.component })
      } catch (error) {
        logError(ERROR_TYPES.RENDER_ERROR, 'Component render error', { 
          error: error.message,
          component: this.component 
        })
        return h(fallbackComponent, { class: 'safe-component-error' }, '组件渲染错误')
      }
    }
  }
}

/**
 * Vue 全局错误处理器
 * @param {Error} error 错误对象
 * @param {Object} instance Vue 实例
 * @param {string} info 错误信息
 */
export function globalErrorHandler(error, instance, info) {
  // 检查是否是 subTree 相关错误
  if (error.message && error.message.includes('subTree')) {
    logError(ERROR_TYPES.SUBTREE_NULL, error.message, {
      instance: instance?.$options?.name || 'Unknown',
      info,
      stack: error.stack
    })
    
    // 尝试恢复组件状态
    if (instance && instance.$forceUpdate) {
      setTimeout(() => {
        try {
          instance.$forceUpdate()
        } catch (updateError) {
          logError(ERROR_TYPES.LIFECYCLE_ERROR, 'Force update failed', {
            error: updateError.message
          })
        }
      }, 100)
    }
  } else {
    logError(ERROR_TYPES.RENDER_ERROR, error.message, {
      instance: instance?.$options?.name || 'Unknown',
      info,
      stack: error.stack
    })
  }
  
  // 在开发环境中显示详细错误信息
  if (process.env.NODE_ENV === 'development') {
    console.group('🚨 Vue Error Details')
    console.error('Error:', error)
    console.error('Instance:', instance)
    console.error('Info:', info)
    console.groupEnd()
  }
}

/**
 * 组件生命周期错误处理
 * @param {string} lifecycle 生命周期名称
 * @param {Function} callback 回调函数
 * @returns {Function} 安全的生命周期函数
 */
export function safeLifecycle(lifecycle, callback) {
  return function(...args) {
    try {
      return callback.apply(this, args)
    } catch (error) {
      logError(ERROR_TYPES.LIFECYCLE_ERROR, `Error in ${lifecycle}`, {
        error: error.message,
        component: this.$options?.name || 'Unknown'
      })
      
      // 在开发环境中重新抛出错误以便调试
      if (process.env.NODE_ENV === 'development') {
        throw error
      }
    }
  }
}

/**
 * 创建错误边界组件
 * @param {string} fallbackMessage 后备消息
 * @returns {Object} 错误边界组件
 */
export function createErrorBoundary(fallbackMessage = '组件加载失败') {
  return {
    name: 'ErrorBoundary',
    data() {
      return {
        hasError: false,
        error: null
      }
    },
    errorCaptured(error, instance, info) {
      this.hasError = true
      this.error = error
      
      logError(ERROR_TYPES.RENDER_ERROR, 'Error boundary caught error', {
        error: error.message,
        instance: instance?.$options?.name || 'Unknown',
        info
      })
      
      return false // 阻止错误继续传播
    },
    render() {
      if (this.hasError) {
        return h('div', { 
          class: 'error-boundary',
          style: {
            padding: '20px',
            border: '1px solid #ff4757',
            borderRadius: '4px',
            backgroundColor: '#fff5f5',
            color: '#ff4757',
            textAlign: 'center'
          }
        }, [
          h('p', fallbackMessage),
          process.env.NODE_ENV === 'development' && this.error ? 
            h('details', [
              h('summary', '错误详情'),
              h('pre', { style: { textAlign: 'left', fontSize: '12px' } }, this.error.stack)
            ]) : null
        ])
      }
      
      return this.$slots.default?.()
    }
  }
}

/**
 * 诊断组件状态
 * @param {Object} component 组件对象
 * @returns {Object} 诊断结果
 */
export function diagnoseComponent(component) {
  const diagnosis = {
    isValid: true,
    issues: [],
    warnings: []
  }
  
  if (!component) {
    diagnosis.isValid = false
    diagnosis.issues.push('组件对象为空')
    return diagnosis
  }
  
  if (!component.type) {
    diagnosis.isValid = false
    diagnosis.issues.push('组件类型缺失')
  }
  
  if (!component.id) {
    diagnosis.warnings.push('组件ID缺失')
  }
  
  if (!component.props) {
    diagnosis.warnings.push('组件属性为空')
  }
  
  return diagnosis
}

// 导出默认配置
export default {
  ERROR_TYPES,
  logError,
  getErrorLogs,
  clearErrorLogs,
  isSafeToRender,
  isSafeDynamicComponent,
  createSafeComponent,
  globalErrorHandler,
  safeLifecycle,
  createErrorBoundary,
  diagnoseComponent
}
