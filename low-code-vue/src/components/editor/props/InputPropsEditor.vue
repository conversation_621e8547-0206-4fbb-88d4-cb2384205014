<template>
  <div class="input-props-editor">
    <el-form label-position="top">
      <el-form-item label="占位文本">
        <el-input
          v-model="componentProps.placeholder"
          placeholder="请输入占位文本"
          @change="updateProps"
        />
      </el-form-item>

      <el-form-item label="默认值">
        <el-input
          v-model="componentProps.defaultValue"
          placeholder="请输入默认值"
          @change="updateProps"
        />
      </el-form-item>

      <el-form-item label="最大长度">
        <el-input-number
          v-model="componentProps.maxLength"
          :min="0"
          :max="1000"
          placeholder="不限制"
          @change="updateProps"
        />
        <div class="tip-text">设置为0表示不限制长度</div>
      </el-form-item>

      <el-form-item label="输入框类型">
        <el-select v-model="componentProps.type" @change="updateProps">
          <el-option label="文本" value="text" />
          <el-option label="密码" value="password" />
          <el-option label="邮箱" value="email" />
          <el-option label="电话" value="tel" />
          <el-option label="网址" value="url" />
          <el-option label="数字" value="number" />
        </el-select>
      </el-form-item>

      <el-form-item label="功能选项">
        <el-checkbox-group v-model="functionOptions" @change="updateFunctionOptions">
          <el-checkbox label="clearable">显示清除按钮</el-checkbox>
          <el-checkbox label="showPassword">显示密码切换按钮</el-checkbox>
          <el-checkbox label="disabled">禁用状态</el-checkbox>
          <el-checkbox label="readonly">只读状态</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="前置图标">
        <el-input
          v-model="componentProps.prefixIcon"
          placeholder="例如：User, Search"
          @change="updateProps"
        />
        <div class="tip-text">使用Element Plus图标名称</div>
      </el-form-item>

      <el-form-item label="后置图标">
        <el-input
          v-model="componentProps.suffixIcon"
          placeholder="例如：Search, Calendar"
          @change="updateProps"
        />
        <div class="tip-text">使用Element Plus图标名称</div>
      </el-form-item>

      <el-form-item label="数据属性">
        <el-input
          v-model="componentProps.dataKey"
          placeholder="例如：username, email"
          @change="updateProps"
        />
        <div class="tip-text">用于在事件中引用此组件的数据，如：{{components.dataKey.value}}</div>
      </el-form-item>

      <el-form-item label="验证规则">
        <el-button type="primary" size="small" @click="showValidationDialog">
          配置验证规则
        </el-button>
        <div v-if="componentProps.validation && componentProps.validation.rules" class="validation-summary">
          <el-tag
            v-for="(rule, index) in componentProps.validation.rules"
            :key="index"
            size="small"
            style="margin: 2px"
          >
            {{ getValidationRuleText(rule) }}
          </el-tag>
        </div>
      </el-form-item>
    </el-form>

    <!-- 验证规则配置对话框 -->
    <el-dialog
      v-model="validationDialogVisible"
      title="配置验证规则"
      width="500px"
      append-to-body
    >
      <div class="validation-rules">
        <div
          v-for="(rule, index) in validationRules"
          :key="index"
          class="validation-rule"
        >
          <el-row :gutter="10">
            <el-col :span="8">
              <el-select v-model="rule.type" placeholder="规则类型">
                <el-option label="必填" value="required" />
                <el-option label="最小长度" value="min" />
                <el-option label="最大长度" value="max" />
                <el-option label="邮箱格式" value="email" />
                <el-option label="手机号" value="phone" />
                <el-option label="身份证" value="idcard" />
                <el-option label="正则表达式" value="pattern" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-input
                v-if="rule.type === 'min' || rule.type === 'max'"
                v-model.number="rule.value"
                type="number"
                placeholder="长度"
              />
              <el-input
                v-else-if="rule.type === 'pattern'"
                v-model="rule.value"
                placeholder="正则表达式"
              />
              <span v-else-if="rule.type === 'required'">-</span>
              <span v-else>自动验证</span>
            </el-col>
            <el-col :span="6">
              <el-input v-model="rule.message" placeholder="错误提示" />
            </el-col>
            <el-col :span="2">
              <el-button type="danger" size="small" @click="removeValidationRule(index)">
                删除
              </el-button>
            </el-col>
          </el-row>
        </div>
        
        <el-button type="primary" size="small" @click="addValidationRule">
          添加规则
        </el-button>
      </div>
      
      <template #footer>
        <el-button @click="validationDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveValidationRules">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// 创建组件属性的响应式副本
const componentProps = ref({
  placeholder: '请输入内容',
  defaultValue: '',
  maxLength: 0,
  type: 'text',
  clearable: true,
  showPassword: false,
  disabled: false,
  readonly: false,
  prefixIcon: '',
  suffixIcon: '',
  dataKey: '',
  validation: null,
  ...props.component.props
})

// 功能选项
const functionOptions = ref([])

// 验证规则对话框
const validationDialogVisible = ref(false)
const validationRules = ref([])

// 初始化功能选项
const initFunctionOptions = () => {
  const options = []
  if (componentProps.value.clearable) options.push('clearable')
  if (componentProps.value.showPassword) options.push('showPassword')
  if (componentProps.value.disabled) options.push('disabled')
  if (componentProps.value.readonly) options.push('readonly')
  functionOptions.value = options
}

// 更新功能选项
const updateFunctionOptions = () => {
  componentProps.value.clearable = functionOptions.value.includes('clearable')
  componentProps.value.showPassword = functionOptions.value.includes('showPassword')
  componentProps.value.disabled = functionOptions.value.includes('disabled')
  componentProps.value.readonly = functionOptions.value.includes('readonly')
  updateProps()
}

// 更新属性
const updateProps = () => {
  const updatedComponent = {
    ...props.component,
    props: { ...componentProps.value }
  }
  emit('update', updatedComponent)
}

// 显示验证规则对话框
const showValidationDialog = () => {
  if (componentProps.value.validation && componentProps.value.validation.rules) {
    validationRules.value = [...componentProps.value.validation.rules]
  } else {
    validationRules.value = []
  }
  validationDialogVisible.value = true
}

// 添加验证规则
const addValidationRule = () => {
  validationRules.value.push({
    type: 'required',
    value: '',
    message: '此字段为必填项'
  })
}

// 删除验证规则
const removeValidationRule = (index) => {
  validationRules.value.splice(index, 1)
}

// 保存验证规则
const saveValidationRules = () => {
  componentProps.value.validation = {
    rules: validationRules.value.filter(rule => rule.type && rule.message)
  }
  validationDialogVisible.value = false
  updateProps()
}

// 获取验证规则文本
const getValidationRuleText = (rule) => {
  switch (rule.type) {
    case 'required':
      return '必填'
    case 'min':
      return `最小长度${rule.value}`
    case 'max':
      return `最大长度${rule.value}`
    case 'email':
      return '邮箱格式'
    case 'phone':
      return '手机号格式'
    case 'idcard':
      return '身份证格式'
    case 'pattern':
      return '自定义格式'
    default:
      return rule.type
  }
}

// 监听组件变化
watch(() => props.component, () => {
  componentProps.value = {
    placeholder: '请输入内容',
    defaultValue: '',
    maxLength: 0,
    type: 'text',
    clearable: true,
    showPassword: false,
    disabled: false,
    readonly: false,
    prefixIcon: '',
    suffixIcon: '',
    dataKey: '',
    validation: null,
    ...props.component.props
  }
  initFunctionOptions()
}, { deep: true })

// 初始化
initFunctionOptions()
</script>

<style scoped>
.input-props-editor {
  padding: 10px;
}

.tip-text {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.validation-summary {
  margin-top: 10px;
}

.validation-rules {
  max-height: 400px;
  overflow-y: auto;
}

.validation-rule {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.el-checkbox) {
  margin-right: 0;
}
</style>
