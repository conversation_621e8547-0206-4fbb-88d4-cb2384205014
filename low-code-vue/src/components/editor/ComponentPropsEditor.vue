<template>
  <div class="props-editor">
    <!-- 添加安全检查，防止 editorComponent 为 null 时的 subTree 错误 -->
    <component
      v-if="editorComponent"
      :is="editorComponent"
      :component="component"
      @update="updateComponent"
    />
    <!-- 当没有对应的编辑器时显示通用编辑器 -->
    <common-props-editor
      v-else
      :component="component"
      @update="updateComponent"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { isSafeToRender, isSafeDynamicComponent, logError, ERROR_TYPES } from '../../utils/vueErrorHandler'
import TextPropsEditor from './props/TextPropsEditor.vue'
import ImagePropsEditor from './props/ImagePropsEditor.vue'
import ButtonPropsEditor from './props/ButtonPropsEditor.vue'
import ContainerPropsEditor from './props/ContainerPropsEditor.vue'
import RadioPropsEditor from './props/RadioPropsEditor.vue'
import CheckboxPropsEditor from './props/CheckboxPropsEditor.vue'
import CarouselPropsEditor from './props/CarouselPropsEditor.vue'
import CollapsePropsEditor from './props/CollapsePropsEditor.vue'
import DataComponentPropsEditor from './props/DataComponentPropsEditor.vue'
import CommonPropsEditor from './props/CommonPropsEditor.vue'
import SpacePropsEditor from './props/SpacePropsEditor.vue'
import InputPropsEditor from './props/InputPropsEditor.vue'

// 新增容器组件编辑器
import CardContainerPropsEditor from './props/CardContainerPropsEditor.vue'
import TabContainerPropsEditor from './props/TabContainerPropsEditor.vue'
import CollapseContainerPropsEditor from './props/CollapseContainerPropsEditor.vue'
import GridContainerPropsEditor from './props/GridContainerPropsEditor.vue'

// 新增表单组件编辑器
import TextareaPropsEditor from './props/TextareaPropsEditor.vue'
import NumberInputPropsEditor from './props/NumberInputPropsEditor.vue'
import PasswordInputPropsEditor from './props/PasswordInputPropsEditor.vue'
import SearchInputPropsEditor from './props/SearchInputPropsEditor.vue'

// 自定义组件编辑器
import CustomComponentPropsEditor from './props/CustomComponentPropsEditor.vue'

// Flow component editors
import FlowNodePropsEditor from './props/FlowNodePropsEditor.vue'
import FlowConnectionPropsEditor from './props/FlowConnectionPropsEditor.vue'
import FlowStartPropsEditor from './props/FlowStartPropsEditor.vue'
import FlowEndPropsEditor from './props/FlowEndPropsEditor.vue'
import FlowDecisionPropsEditor from './props/FlowDecisionPropsEditor.vue'
import FlowProcessPropsEditor from './props/FlowProcessPropsEditor.vue'

const props = defineProps({
  component: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// Map component types to editors
const editorMap = {
  text: TextPropsEditor,
  image: ImagePropsEditor,
  button: ButtonPropsEditor,
  input: InputPropsEditor,
  container: ContainerPropsEditor,
  radio: RadioPropsEditor,
  checkbox: CheckboxPropsEditor,
  carousel: CarouselPropsEditor,
  collapse: CollapsePropsEditor,
  space: SpacePropsEditor,

  // 新增容器组件编辑器
  'card-container': CardContainerPropsEditor,
  'tab-container': TabContainerPropsEditor,
  'collapse-container': CollapseContainerPropsEditor,
  'grid-container': GridContainerPropsEditor,

  // 表单组件编辑器
  textarea: TextareaPropsEditor,
  'number-input': NumberInputPropsEditor,
  'password-input': PasswordInputPropsEditor,
  'search-input': SearchInputPropsEditor,

  // 自定义组件编辑器
  custom: CustomComponentPropsEditor,

  // Data component editors
  statistic: DataComponentPropsEditor,
  'line-chart': DataComponentPropsEditor,
  'bar-chart': DataComponentPropsEditor,
  'pie-chart': DataComponentPropsEditor,

  // Flow component editors
  flowNode: FlowNodePropsEditor,
  flowConnection: FlowConnectionPropsEditor,
  flowStart: FlowStartPropsEditor,
  flowEnd: FlowEndPropsEditor,
  flowDecision: FlowDecisionPropsEditor,
  flowProcess: FlowProcessPropsEditor
}

// Get editor component with enhanced error handling
const editorComponent = computed(() => {
  // 使用安全检查工具验证组件
  if (!isSafeToRender(props.component)) {
    logError(ERROR_TYPES.COMPONENT_NULL, 'ComponentPropsEditor: Invalid component', {
      component: props.component
    })
    return null
  }

  const componentType = props.component.type
  const editor = editorMap[componentType]

  // 使用安全检查工具验证动态组件
  if (!isSafeDynamicComponent(editor, componentType)) {
    logError(ERROR_TYPES.DYNAMIC_COMPONENT_ERROR, `ComponentPropsEditor: No editor found for component type: ${componentType}`, {
      componentType,
      availableEditors: Object.keys(editorMap)
    })
    // 返回 null，这样会使用 CommonPropsEditor 作为后备
    return null
  }

  return editor
})

// Update component
const updateComponent = (updatedComponent) => {
  console.log('ComponentPropsEditor updating component:', props.component.id, props.component.type)

  try {
    // If we received a complete component object
    if (updatedComponent.id && updatedComponent.type) {
      console.log('Received complete component update')

      // Create a completely new component object to ensure reactivity
      const newComponent = {
        id: updatedComponent.id,
        type: updatedComponent.type,
        props: JSON.parse(JSON.stringify(updatedComponent.props || {})),
        styles: JSON.parse(JSON.stringify(updatedComponent.styles || {})),
        disabled: updatedComponent.disabled
      }

      // Emit the update event
      emit('update', newComponent)
    }
    // If we just received updated props
    else {
      console.log('Received props update:', JSON.stringify(updatedComponent))

      // Create a completely new component object to ensure reactivity
      // Check if updatedComponent is a complete component object with disabled property
      if (typeof updatedComponent === 'object' && 'disabled' in updatedComponent) {
        const newComponent = {
          id: props.component.id,
          type: props.component.type,
          props: JSON.parse(JSON.stringify(props.component.props || {})),
          styles: JSON.parse(JSON.stringify(props.component.styles || {})),
          disabled: updatedComponent.disabled
        }

        // Emit the update event
        emit('update', newComponent)
      } else {
        // If it's just props update
        const newComponent = {
          id: props.component.id,
          type: props.component.type,
          props: JSON.parse(JSON.stringify(updatedComponent)),
          styles: JSON.parse(JSON.stringify(props.component.styles || {})),
          disabled: props.component.disabled
        }

        // Emit the update event
        emit('update', newComponent)
      }
    }
  } catch (error) {
    console.error('Error updating component in ComponentPropsEditor:', error)
  }
}
</script>

<style scoped>
.props-editor {
  padding: 10px 0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  padding-bottom: 8px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-radio-group),
:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

:deep(.el-radio),
:deep(.el-checkbox) {
  margin-right: 0;
  margin-bottom: 5px;
}
</style>
