import request from './index'

/**
 * 字体管理API
 */
export const fontApi = {
  /**
   * 获取字体列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getFontList(params = {}) {
    return request({
      url: '/font/list',
      method: 'get',
      params
    })
  },

  /**
   * 获取字体详情
   * @param {string} fontId - 字体ID
   * @returns {Promise}
   */
  getFontDetail(fontId) {
    return request({
      url: `/font/${fontId}`,
      method: 'get'
    })
  },

  /**
   * 创建字体
   * @param {Object} fontData - 字体数据
   * @returns {Promise}
   */
  createFont(fontData) {
    return request({
      url: '/font',
      method: 'post',
      data: fontData
    })
  },

  /**
   * 更新字体
   * @param {string} fontId - 字体ID
   * @param {Object} fontData - 字体数据
   * @returns {Promise}
   */
  updateFont(fontId, fontData) {
    return request({
      url: `/font/${fontId}`,
      method: 'put',
      data: fontData
    })
  },

  /**
   * 删除字体
   * @param {string} fontId - 字体ID
   * @returns {Promise}
   */
  deleteFont(fontId) {
    return request({
      url: `/font/${fontId}`,
      method: 'delete'
    })
  },

  /**
   * 批量删除字体
   * @param {Array} fontIds - 字体ID数组
   * @returns {Promise}
   */
  batchDeleteFonts(fontIds) {
    return request({
      url: '/font/batch',
      method: 'delete',
      data: { ids: fontIds }
    })
  },

  /**
   * 上传字体文件
   * @param {File} file - 字体文件
   * @param {string} name - 字体名称
   * @param {string} family - 字体族
   * @param {string} description - 字体描述
   * @param {string} previewText - 预览文本
   * @returns {Promise}
   */
  uploadFont(file, name, family, description = '', previewText = 'Aa') {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('name', name)
    formData.append('family', family)
    if (description) formData.append('description', description)
    if (previewText) formData.append('previewText', previewText)

    return request({
      url: '/fonts/upload',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 添加Web字体
   * @param {string} name - 字体名称
   * @param {string} family - 字体族
   * @param {string} cssUrl - CSS URL
   * @param {string} description - 字体描述
   * @param {string} previewText - 预览文本
   * @returns {Promise}
   */
  addWebFont(name, family, cssUrl, description = '', previewText = 'Aa') {
    const formData = new FormData()
    formData.append('name', name)
    formData.append('family', family)
    formData.append('cssUrl', cssUrl)
    if (description) formData.append('description', description)
    if (previewText) formData.append('previewText', previewText)

    return request({
      url: '/api/fonts/web',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 添加系统字体
   * @param {string} name - 字体名称
   * @param {string} family - 字体族
   * @param {string} description - 字体描述
   * @param {string} previewText - 预览文本
   * @returns {Promise}
   */
  addSystemFont(name, family, description = '', previewText = 'Aa') {
    const formData = new FormData()
    formData.append('name', name)
    formData.append('family', family)
    if (description) formData.append('description', description)
    if (previewText) formData.append('previewText', previewText)

    return request({
      url: '/api/fonts/system',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 分页查询字体列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getFontPage(params) {
    return request({
      url: '/api/fonts/page',
      method: 'get',
      params
    })
  },

  /**
   * 查询启用的字体列表
   * @returns {Promise}
   */
  getEnabledFonts() {
    return request({
      url: '/fonts/enabled',
      method: 'get'
    })
  },

  /**
   * 根据类型查询字体列表
   * @param {string} type - 字体类型
   * @returns {Promise}
   */
  getFontsByType(type) {
    return request({
      url: `/fonts/type/${type}`,
      method: 'get'
    })
  },

  /**
   * 获取字体文件URL
   * @param {string} fileName - 文件名
   * @returns {string}
   */
  getFontFileUrl(fileName) {
    return `${import.meta.env.VITE_APP_API_BASE_URL}/api/fonts/file/${fileName}`
  },

  /**
   * 修复字体URL
   * @returns {Promise}
   */
  fixFontUrls() {
    return request({
      url: '/fonts/fix-urls',
      method: 'post'
    })
  },

  /**
   * 调试文件系统
   * @returns {Promise}
   */
  debugFileSystem() {
    return request({
      url: '/fonts/debug/file-system',
      method: 'get'
    })
  },

  /**
   * 验证字体URL
   * @param {string} url - 字体URL
   * @returns {Promise}
   */
  validateFontUrl(url) {
    return request({
      url: '/font/validate-url',
      method: 'post',
      data: { url }
    })
  },

  /**
   * 获取Web字体建议
   * @param {string} keyword - 搜索关键词
   * @returns {Promise}
   */
  getWebFontSuggestions(keyword) {
    return request({
      url: '/font/web-suggestions',
      method: 'get',
      params: { keyword }
    })
  },

  /**
   * 导入字体配置
   * @param {Object} config - 字体配置
   * @returns {Promise}
   */
  importFontConfig(config) {
    return request({
      url: '/font/import',
      method: 'post',
      data: config
    })
  },

  /**
   * 导出字体配置
   * @param {Array} fontIds - 要导出的字体ID数组（可选）
   * @returns {Promise}
   */
  exportFontConfig(fontIds = []) {
    return request({
      url: '/font/export',
      method: 'post',
      data: { fontIds }
    })
  },

  /**
   * 获取字体使用统计
   * @returns {Promise}
   */
  getFontUsageStats() {
    return request({
      url: '/font/usage-stats',
      method: 'get'
    })
  },

  /**
   * 检查字体是否可用
   * @param {string} fontFamily - 字体族名称
   * @returns {Promise}
   */
  checkFontAvailability(fontFamily) {
    return request({
      url: '/font/check-availability',
      method: 'post',
      data: { fontFamily }
    })
  },

  /**
   * 获取系统字体列表
   * @returns {Promise}
   */
  getSystemFonts() {
    return request({
      url: '/font/system-fonts',
      method: 'get'
    })
  },

  /**
   * 预加载字体
   * @param {Array} fontIds - 字体ID数组
   * @returns {Promise}
   */
  preloadFonts(fontIds) {
    return request({
      url: '/font/preload',
      method: 'post',
      data: { fontIds }
    })
  }
}

/**
 * 字体工具函数
 */
export const fontUtils = {
  /**
   * 检测浏览器是否支持字体
   * @param {string} fontFamily - 字体族名称
   * @returns {boolean}
   */
  isFontSupported(fontFamily) {
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')
    
    // 使用默认字体绘制文本
    context.font = '12px monospace'
    const defaultWidth = context.measureText('test').width
    
    // 使用指定字体绘制文本
    context.font = `12px ${fontFamily}, monospace`
    const testWidth = context.measureText('test').width
    
    // 如果宽度不同，说明字体被加载
    return defaultWidth !== testWidth
  },

  /**
   * 加载Web字体
   * @param {string} url - 字体CSS文件URL
   * @returns {Promise}
   */
  loadWebFont(url) {
    return new Promise((resolve, reject) => {
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = url
      
      link.onload = () => resolve(true)
      link.onerror = () => reject(new Error('Failed to load font'))
      
      document.head.appendChild(link)
      
      // 设置超时
      setTimeout(() => {
        reject(new Error('Font loading timeout'))
      }, 10000)
    })
  },

  /**
   * 获取字体预览文本
   * @param {string} type - 字体类型
   * @returns {string}
   */
  getPreviewText(type = 'latin') {
    const previewTexts = {
      latin: 'The quick brown fox jumps over the lazy dog.',
      chinese: '快速的棕色狐狸跳过懒狗。',
      numbers: '0123456789',
      mixed: 'Aa Bb Cc 中文 123'
    }
    
    return previewTexts[type] || previewTexts.mixed
  },

  /**
   * 生成字体CSS规则
   * @param {Object} font - 字体对象
   * @returns {string}
   */
  generateFontCSS(font) {
    if (font.type === 'web' && font.url) {
      return `@import url('${font.url}');`
    }
    return ''
  },

  /**
   * 验证字体族名称格式
   * @param {string} fontFamily - 字体族名称
   * @returns {boolean}
   */
  validateFontFamily(fontFamily) {
    if (!fontFamily || typeof fontFamily !== 'string') {
      return false
    }
    
    // 基本格式验证
    const fontFamilyRegex = /^[a-zA-Z0-9\s\-_'",.()]+$/
    return fontFamilyRegex.test(fontFamily.trim())
  }
}

// 字体类型常量
export const FONT_TYPES = {
  SYSTEM: 'system',    // 系统字体
  WEB: 'web',         // Web字体
  CUSTOM: 'custom'    // 自定义字体
}

// 字体类型名称映射
export const FONT_TYPE_NAMES = {
  [FONT_TYPES.SYSTEM]: '系统字体',
  [FONT_TYPES.WEB]: 'Web字体',
  [FONT_TYPES.CUSTOM]: '自定义字体'
}

// 支持的字体文件格式
export const SUPPORTED_FONT_FORMATS = ['.ttf', '.otf', '.woff', '.woff2']

// 字体文件大小限制 (10MB)
export const MAX_FONT_FILE_SIZE = 10 * 1024 * 1024

export default fontApi
