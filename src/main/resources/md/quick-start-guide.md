# 组件事件快速入门指南

## 🚀 5分钟快速上手

### 场景：创建一个简单的用户注册表单

我们将创建一个包含用户名输入框和提交按钮的简单表单，演示如何配置组件属性和事件。

## 📝 步骤详解

### 第1步：添加输入框组件

1. **拖拽组件**：从左侧组件面板拖拽"输入框"到画布
2. **设置组件ID**：在属性面板中设置组件ID为`username-input`
3. **配置基础属性**：
   - 占位文本：`请输入用户名`
   - 最大长度：`20`
   - 显示清除按钮：✓
   - 数据属性：`username`

**属性配置JSON**：
```json
{
  "placeholder": "请输入用户名",
  "maxLength": 20,
  "clearable": true,
  "dataKey": "username"
}
```

### 第2步：添加提交按钮

1. **拖拽组件**：从左侧组件面板拖拽"按钮"到画布
2. **设置组件ID**：在属性面板中设置组件ID为`submit-btn`
3. **配置基础属性**：
   - 按钮文本：`提交`
   - 按钮类型：`primary`

**属性配置JSON**：
```json
{
  "text": "提交",
  "type": "primary"
}
```

### 第3步：配置输入框事件

1. **切换到事件选项卡**
2. **添加事件**：点击"添加事件"按钮
3. **配置事件**：
   - 事件类型：`输入变化`
   - 动作类型：`设置变量`
   - 变量名称：`currentUsername`
   - 变量值：`{{event.target.value}}`

**事件配置JSON**：
```json
[
  {
    "type": "input",
    "action": {
      "type": "setVariable",
      "variableName": "currentUsername",
      "variableValue": "{{event.target.value}}"
    }
  }
]
```

### 第4步：配置按钮点击事件

1. **选中提交按钮**
2. **切换到事件选项卡**
3. **添加点击事件**：
   - 事件类型：`点击`
   - 动作类型：`执行JavaScript`
   - JavaScript代码：

```javascript
// 获取用户名
const username = getVariable('currentUsername');

// 验证用户名
if (!username || username.trim() === '') {
  showMessage('error', '请输入用户名');
  return;
}

if (username.length < 3) {
  showMessage('error', '用户名至少3个字符');
  return;
}

// 显示成功消息
showMessage('success', `欢迎，${username}！`);

// 这里可以调用API提交数据
// callApi('/api/user/register', 'POST', {username: username});
```

## 🎯 运行效果

完成上述配置后，您的表单将具备以下功能：

1. **实时数据捕获**：用户在输入框中输入时，数据会实时存储到变量中
2. **输入验证**：点击提交按钮时会验证用户名是否为空和长度
3. **用户反馈**：根据验证结果显示相应的成功或错误消息

## 🔧 进阶功能

### 添加API调用

将按钮事件中的JavaScript代码替换为：

```javascript
// 获取用户名
const username = getVariable('currentUsername');

// 验证用户名
if (!username || username.trim() === '') {
  showMessage('error', '请输入用户名');
  return;
}

// 禁用按钮防止重复提交
disableComponent('submit-btn');
setComponentValue('submit-btn', '提交中...');

// 调用API
callApi('/api/user/check', 'POST', {username: username})
  .then(result => {
    if (result.success) {
      showMessage('success', '用户名可用');
    } else {
      showMessage('error', result.message || '用户名已存在');
    }
  })
  .catch(error => {
    showMessage('error', '网络错误，请重试');
  })
  .finally(() => {
    // 恢复按钮状态
    enableComponent('submit-btn');
    setComponentValue('submit-btn', '提交');
  });
```

### 添加实时验证

为输入框添加失去焦点事件：

```javascript
const username = event.target.value;

if (username && username.length < 3) {
  showMessage('warning', '用户名至少需要3个字符');
} else if (username && username.length > 20) {
  showMessage('warning', '用户名不能超过20个字符');
}
```

## 📋 常用事件配置模板

### 1. 表单验证模板

```json
{
  "type": "blur",
  "action": {
    "type": "javascript",
    "jsCode": "const value = event.target.value;\nif (!value) {\n  showMessage('error', '此字段不能为空');\n}"
  }
}
```

### 2. API调用模板

```json
{
  "type": "click",
  "action": {
    "type": "api",
    "apiUrl": "/api/data",
    "apiMethod": "post",
    "apiParams": "{\"key\": \"{{variables.value}}\"}",
    "dataKey": "result",
    "successMessage": "操作成功",
    "errorMessage": "操作失败"
  }
}
```

### 3. 页面跳转模板

```json
{
  "type": "click",
  "action": {
    "type": "navigate",
    "navigationType": "page",
    "pageId": "target-page-id"
  }
}
```

### 4. 组件控制模板

```json
{
  "type": "click",
  "action": {
    "type": "toggleComponent",
    "targetComponentId": "target-component-id",
    "operation": "toggle"
  }
}
```

## 🎨 样式配置技巧

### 输入框样式

```json
{
  "width": "300px",
  "height": "40px",
  "margin": "10px 0",
  "padding": "0 12px",
  "fontSize": "16px",
  "border": "2px solid #409eff",
  "borderRadius": "8px"
}
```

### 按钮样式

```json
{
  "width": "120px",
  "height": "40px",
  "margin": "20px 0",
  "fontSize": "16px",
  "borderRadius": "8px"
}
```

## 🚨 常见错误避免

### 1. 组件ID重复
- ❌ 错误：多个组件使用相同的ID
- ✅ 正确：每个组件使用唯一的ID

### 2. 事件引用错误
- ❌ 错误：`{{components.wrong-id.value}}`
- ✅ 正确：`getComponentValue('correct-id')`

### 3. API参数格式错误
- ❌ 错误：`{key: value}` (缺少引号)
- ✅ 正确：`{"key": "value"}` (标准JSON格式)

### 4. 变量名称不规范
- ❌ 错误：`user name` (包含空格)
- ✅ 正确：`userName` (驼峰命名)

## 🔗 下一步学习

完成快速入门后，建议继续学习：

1. [组件事件详细指南](./component-events-guide.md) - 深入了解所有事件类型和动作
2. [输入框组件文档](./input-component.md) - 学习输入框的高级功能
3. [按钮组件文档](./button-component.md) - 掌握按钮的各种配置
4. [API调用规范](./api-specification.md) - 了解如何正确调用后端接口

## 💡 小贴士

- **保存习惯**：配置完成后记得保存页面
- **预览测试**：使用预览功能测试事件是否正常工作
- **控制台调试**：使用浏览器开发者工具查看错误信息
- **渐进增强**：先实现基础功能，再逐步添加高级特性
