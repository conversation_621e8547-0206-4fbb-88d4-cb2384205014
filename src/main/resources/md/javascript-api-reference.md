# JavaScript API 参考文档

## 📋 概述

本文档详细介绍了在低代码平台中可以使用的JavaScript API函数，这些函数可以在事件的JavaScript代码中调用。

## 🔧 组件操作API

### getComponentValue(componentId)

获取指定组件的当前值。

**参数**：
- `componentId` (string): 组件ID

**返回值**：
- 组件的当前值

**示例**：
```javascript
const username = getComponentValue('username-input');
const selectedOption = getComponentValue('category-select');
```

### setComponentValue(componentId, value)

设置指定组件的值。

**参数**：
- `componentId` (string): 组件ID
- `value` (any): 要设置的值

**示例**：
```javascript
setComponentValue('result-text', '处理完成');
setComponentValue('progress-bar', 75);
setComponentValue('status-select', 'active');
```

### getComponentData(componentId)

获取指定组件的数据（适用于列表、表格等数据组件）。

**参数**：
- `componentId` (string): 组件ID

**返回值**：
- 组件的数据数组或对象

**示例**：
```javascript
const tableData = getComponentData('user-table');
const chartData = getComponentData('sales-chart');
```

### setComponentData(componentId, data)

设置指定组件的数据。

**参数**：
- `componentId` (string): 组件ID
- `data` (array|object): 要设置的数据

**示例**：
```javascript
setComponentData('user-table', [
  {id: 1, name: '张三', age: 25},
  {id: 2, name: '李四', age: 30}
]);

setComponentData('pie-chart', {
  labels: ['销售', '市场', '技术'],
  values: [30, 25, 45]
});
```

### showComponent(componentId)

显示指定组件。

**参数**：
- `componentId` (string): 组件ID

**示例**：
```javascript
showComponent('success-message');
showComponent('admin-panel');
```

### hideComponent(componentId)

隐藏指定组件。

**参数**：
- `componentId` (string): 组件ID

**示例**：
```javascript
hideComponent('loading-spinner');
hideComponent('error-panel');
```

### toggleComponent(componentId)

切换组件的显示/隐藏状态。

**参数**：
- `componentId` (string): 组件ID

**示例**：
```javascript
toggleComponent('advanced-options');
toggleComponent('sidebar-menu');
```

### enableComponent(componentId)

启用指定组件。

**参数**：
- `componentId` (string): 组件ID

**示例**：
```javascript
enableComponent('submit-button');
enableComponent('form-inputs');
```

### disableComponent(componentId)

禁用指定组件。

**参数**：
- `componentId` (string): 组件ID

**示例**：
```javascript
disableComponent('submit-button');
disableComponent('delete-button');
```

## 💾 数据管理API

### setVariable(name, value)

设置全局变量。

**参数**：
- `name` (string): 变量名
- `value` (any): 变量值

**示例**：
```javascript
setVariable('currentUser', {id: 1, name: '张三'});
setVariable('pageSize', 10);
setVariable('isLoggedIn', true);
```

### getVariable(name)

获取全局变量的值。

**参数**：
- `name` (string): 变量名

**返回值**：
- 变量的值，如果不存在则返回undefined

**示例**：
```javascript
const currentUser = getVariable('currentUser');
const pageSize = getVariable('pageSize') || 10;
const isLoggedIn = getVariable('isLoggedIn') || false;
```

### removeVariable(name)

删除全局变量。

**参数**：
- `name` (string): 变量名

**示例**：
```javascript
removeVariable('tempData');
removeVariable('searchCache');
```

### getApiData(dataKey)

获取API调用返回的数据。

**参数**：
- `dataKey` (string): 数据键名

**返回值**：
- API返回的数据

**示例**：
```javascript
const userList = getApiData('userList');
const searchResults = getApiData('searchResults');
```

### setApiData(dataKey, data)

设置API数据（用于模拟或缓存）。

**参数**：
- `dataKey` (string): 数据键名
- `data` (any): 数据内容

**示例**：
```javascript
setApiData('userList', mockUserData);
setApiData('config', defaultConfig);
```

## 🌐 网络请求API

### callApi(url, method, data, options)

调用API接口。

**参数**：
- `url` (string): 请求URL
- `method` (string): 请求方法 ('GET', 'POST', 'PUT', 'DELETE')
- `data` (object): 请求数据
- `options` (object): 可选配置

**返回值**：
- Promise对象

**示例**：
```javascript
// GET请求
const users = await callApi('/api/users', 'GET');

// POST请求
const result = await callApi('/api/users', 'POST', {
  name: '张三',
  email: '<EMAIL>'
});

// 带选项的请求
const data = await callApi('/api/data', 'GET', null, {
  timeout: 5000,
  headers: {'Authorization': 'Bearer token'}
});
```

### get(url, params)

发送GET请求的简化方法。

**参数**：
- `url` (string): 请求URL
- `params` (object): 查询参数

**示例**：
```javascript
const users = await get('/api/users', {page: 1, size: 10});
const user = await get('/api/users/123');
```

### post(url, data)

发送POST请求的简化方法。

**参数**：
- `url` (string): 请求URL
- `data` (object): 请求数据

**示例**：
```javascript
const result = await post('/api/users', {
  name: '张三',
  email: '<EMAIL>'
});
```

### put(url, data)

发送PUT请求的简化方法。

**参数**：
- `url` (string): 请求URL
- `data` (object): 请求数据

**示例**：
```javascript
const result = await put('/api/users/123', {
  name: '张三',
  email: '<EMAIL>'
});
```

### delete(url, params)

发送DELETE请求的简化方法。

**参数**：
- `url` (string): 请求URL
- `params` (object): 查询参数

**示例**：
```javascript
const result = await delete('/api/users/123');
```

## 💬 用户界面API

### showMessage(type, message, duration)

显示消息提示。

**参数**：
- `type` (string): 消息类型 ('success', 'warning', 'error', 'info')
- `message` (string): 消息内容
- `duration` (number): 显示时长（毫秒），可选

**示例**：
```javascript
showMessage('success', '保存成功');
showMessage('error', '网络连接失败');
showMessage('warning', '请检查输入内容', 5000);
```

### showConfirm(message, callback)

显示确认对话框。

**参数**：
- `message` (string): 确认消息
- `callback` (function): 回调函数，参数为确认结果(boolean)

**示例**：
```javascript
showConfirm('确定要删除这条记录吗？', (confirmed) => {
  if (confirmed) {
    // 执行删除操作
    deleteRecord();
  }
});
```

### showPrompt(message, defaultValue, callback)

显示输入对话框。

**参数**：
- `message` (string): 提示消息
- `defaultValue` (string): 默认值
- `callback` (function): 回调函数，参数为输入值

**示例**：
```javascript
showPrompt('请输入新的名称：', '默认名称', (value) => {
  if (value) {
    setComponentValue('name-input', value);
  }
});
```

### showLoading(message)

显示加载提示。

**参数**：
- `message` (string): 加载消息，可选

**示例**：
```javascript
showLoading('正在处理...');
```

### hideLoading()

隐藏加载提示。

**示例**：
```javascript
hideLoading();
```

## 🧭 导航API

### navigateTo(path)

跳转到指定页面。

**参数**：
- `path` (string): 页面路径或URL

**示例**：
```javascript
navigateTo('/dashboard');
navigateTo('https://example.com');
navigateTo('/users/123');
```

### refreshPage()

刷新当前页面。

**示例**：
```javascript
refreshPage();
```

### goBack()

返回上一页。

**示例**：
```javascript
goBack();
```

## 🔧 工具函数API

### formatDate(date, format)

格式化日期。

**参数**：
- `date` (Date|string): 日期对象或日期字符串
- `format` (string): 格式字符串，可选

**返回值**：
- 格式化后的日期字符串

**示例**：
```javascript
const formatted = formatDate(new Date(), 'YYYY-MM-DD');
const time = formatDate('2024-01-01', 'MM/DD/YYYY');
```

### formatCurrency(amount, currency)

格式化货币。

**参数**：
- `amount` (number): 金额
- `currency` (string): 货币代码，默认'CNY'

**返回值**：
- 格式化后的货币字符串

**示例**：
```javascript
const price = formatCurrency(1234.56); // ¥1,234.56
const usd = formatCurrency(1234.56, 'USD'); // $1,234.56
```

### validateEmail(email)

验证邮箱格式。

**参数**：
- `email` (string): 邮箱地址

**返回值**：
- boolean: 是否为有效邮箱

**示例**：
```javascript
const isValid = validateEmail('<EMAIL>'); // true
```

### validatePhone(phone)

验证手机号格式。

**参数**：
- `phone` (string): 手机号

**返回值**：
- boolean: 是否为有效手机号

**示例**：
```javascript
const isValid = validatePhone('13800138000'); // true
```

## 🎯 事件API

### triggerEvent(eventName, data)

触发自定义事件。

**参数**：
- `eventName` (string): 事件名称
- `data` (any): 事件数据

**示例**：
```javascript
triggerEvent('userSelected', {id: 123, name: '张三'});
triggerEvent('dataUpdated', newData);
```

### addEventListener(eventName, callback)

监听自定义事件。

**参数**：
- `eventName` (string): 事件名称
- `callback` (function): 事件处理函数

**示例**：
```javascript
addEventListener('userSelected', (userData) => {
  setComponentData('user-detail', userData);
});
```

## 📝 使用示例

### 完整的表单处理示例

```javascript
// 获取表单数据
const formData = {
  username: getComponentValue('username-input'),
  email: getComponentValue('email-input'),
  phone: getComponentValue('phone-input')
};

// 验证数据
if (!formData.username) {
  showMessage('error', '用户名不能为空');
  return;
}

if (!validateEmail(formData.email)) {
  showMessage('error', '邮箱格式不正确');
  return;
}

if (!validatePhone(formData.phone)) {
  showMessage('error', '手机号格式不正确');
  return;
}

// 显示加载状态
showLoading('正在提交...');
disableComponent('submit-button');

try {
  // 提交数据
  const result = await post('/api/users', formData);
  
  if (result.success) {
    showMessage('success', '注册成功');
    navigateTo('/login');
  } else {
    showMessage('error', result.message || '注册失败');
  }
} catch (error) {
  showMessage('error', '网络错误，请重试');
} finally {
  // 恢复状态
  hideLoading();
  enableComponent('submit-button');
}
```
