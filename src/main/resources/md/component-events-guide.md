# 组件事件使用详细指南

## 📋 概述

本文档详细介绍如何在低代码平台中配置和使用组件事件，包括数据属性定义、数据引用方式、JavaScript代码执行等高级功能。

## 🎯 核心概念

### 1. 事件系统架构

```
组件 → 事件触发 → 动作执行 → 数据处理 → 界面更新
```

### 2. 数据流向

```
输入组件 → 数据存储 → 数据引用 → 其他组件
```

## 🔧 基础配置

### 事件类型

| 事件类型 | 适用组件 | 触发时机 | 说明 |
|---------|----------|----------|------|
| `click` | 按钮、图片等 | 用户点击时 | 最常用的交互事件 |
| `input` | 输入框、文本域 | 输入内容变化时 | 实时响应用户输入 |
| `change` | 选择器、开关 | 值改变时 | 值确定改变后触发 |
| `blur` | 表单组件 | 失去焦点时 | 常用于验证 |
| `focus` | 表单组件 | 获得焦点时 | 用户开始输入时 |
| `submit` | 表单 | 表单提交时 | 表单数据提交 |

### 动作类型

| 动作类型 | 说明 | 使用场景 |
|---------|------|----------|
| `navigate` | 页面跳转 | 导航到其他页面或外部链接 |
| `message` | 显示消息 | 用户反馈和提示 |
| `api` | 调用API | 数据获取和提交 |
| `toggleComponent` | 切换组件状态 | 显示/隐藏组件 |
| `setVariable` | 设置变量 | 存储临时数据 |
| `javascript` | 执行JavaScript | 自定义逻辑处理 |

## 💡 实战示例

### 示例1：输入框 + 按钮提交

**场景**：用户在输入框中输入用户名，点击按钮提交到后端验证

#### 步骤1：配置输入框组件

**组件ID**: `username-input`
**组件类型**: `input`

**属性配置**：
```json
{
  "placeholder": "请输入用户名",
  "clearable": true,
  "maxLength": 20,
  "dataKey": "username"
}
```

**事件配置**：
```json
[
  {
    "type": "input",
    "action": {
      "type": "setVariable",
      "variableName": "currentUsername",
      "variableValue": "{{event.target.value}}"
    }
  }
]
```

#### 步骤2：配置提交按钮

**组件ID**: `submit-btn`
**组件类型**: `button`

**属性配置**：
```json
{
  "text": "提交",
  "type": "primary"
}
```

**事件配置**：
```json
[
  {
    "type": "click",
    "action": {
      "type": "api",
      "apiUrl": "/api/user/validate",
      "apiMethod": "post",
      "apiParams": "{\"username\": \"{{variables.currentUsername}}\"}",
      "dataKey": "validationResult",
      "successMessage": "验证成功",
      "errorMessage": "验证失败",
      "showSuccessMessage": true
    }
  }
]
```

### 示例2：搜索功能实现

**场景**：实时搜索，用户输入关键词时自动调用搜索API并更新结果列表

#### 步骤1：配置搜索输入框

**组件ID**: `search-input`

**事件配置**：
```json
[
  {
    "type": "input",
    "action": {
      "type": "api",
      "apiUrl": "/api/search",
      "apiMethod": "get",
      "apiParams": "{\"keyword\": \"{{event.target.value}}\", \"page\": 1, \"size\": 10}",
      "dataKey": "searchResults",
      "dataBindingTarget": {
        "componentId": "result-list",
        "property": "data"
      }
    }
  }
]
```

#### 步骤2：配置结果列表组件

**组件ID**: `result-list`
**组件类型**: `list`

**数据绑定**：
```json
{
  "dataSource": "{{apiData.searchResults}}"
}
```

## 🔍 数据引用方式

### 1. 组件数据引用

```javascript
// 引用特定组件的值
"{{components.componentId.value}}"

// 引用输入框的值
"{{components.username-input.value}}"

// 引用选择器的选中值
"{{components.user-select.selectedValue}}"
```

### 2. API数据引用

```javascript
// 引用API返回的数据
"{{apiData.dataKey}}"

// 引用搜索结果
"{{apiData.searchResults}}"

// 引用用户信息
"{{apiData.userInfo.name}}"
```

### 3. 变量引用

```javascript
// 引用设置的变量
"{{variables.variableName}}"

// 引用当前用户名
"{{variables.currentUsername}}"

// 引用页面状态
"{{variables.pageStatus}}"
```

### 4. 事件数据引用

```javascript
// 引用事件目标的值
"{{event.target.value}}"

// 引用事件详细信息
"{{event.detail}}"

// 引用表单数据
"{{event.formData}}"
```

## 🚀 JavaScript代码使用

### 基础语法

```javascript
// 获取组件值
const username = getComponentValue('username-input');

// 设置组件值
setComponentValue('result-text', '处理完成');

// 显示消息
showMessage('success', '操作成功');

// 调用API
const result = await callApi('/api/data', 'GET', {});

// 设置变量
setVariable('status', 'completed');

// 获取变量
const status = getVariable('status');
```

### 高级示例

#### 表单验证

```javascript
// 获取表单数据
const username = getComponentValue('username-input');
const email = getComponentValue('email-input');
const password = getComponentValue('password-input');

// 验证逻辑
if (!username || username.length < 3) {
  showMessage('error', '用户名至少3个字符');
  return;
}

if (!email || !email.includes('@')) {
  showMessage('error', '请输入有效的邮箱地址');
  return;
}

if (!password || password.length < 6) {
  showMessage('error', '密码至少6个字符');
  return;
}

// 提交数据
try {
  const result = await callApi('/api/user/register', 'POST', {
    username: username,
    email: email,
    password: password
  });
  
  if (result.success) {
    showMessage('success', '注册成功');
    // 跳转到登录页面
    navigateTo('/login');
  } else {
    showMessage('error', result.message || '注册失败');
  }
} catch (error) {
  showMessage('error', '网络错误，请重试');
}
```

#### 动态数据处理

```javascript
// 获取API数据
const apiResult = getApiData('userList');

if (apiResult && apiResult.length > 0) {
  // 处理数据
  const processedData = apiResult.map(user => ({
    ...user,
    displayName: `${user.firstName} ${user.lastName}`,
    isActive: user.status === 'active'
  }));
  
  // 更新组件数据
  setComponentData('user-table', processedData);
  
  // 更新统计信息
  setComponentValue('total-count', processedData.length);
  setComponentValue('active-count', processedData.filter(u => u.isActive).length);
} else {
  // 显示空状态
  setComponentValue('empty-message', '暂无用户数据');
  showComponent('empty-state');
  hideComponent('user-table');
}
```

## 🛠️ 内置函数参考

### 组件操作函数

```javascript
// 获取组件值
getComponentValue(componentId)

// 设置组件值
setComponentValue(componentId, value)

// 获取组件数据
getComponentData(componentId)

// 设置组件数据
setComponentData(componentId, data)

// 显示组件
showComponent(componentId)

// 隐藏组件
hideComponent(componentId)

// 切换组件显示状态
toggleComponent(componentId)

// 启用组件
enableComponent(componentId)

// 禁用组件
disableComponent(componentId)
```

### 数据操作函数

```javascript
// 设置变量
setVariable(name, value)

// 获取变量
getVariable(name)

// 删除变量
removeVariable(name)

// 获取API数据
getApiData(dataKey)

// 设置API数据
setApiData(dataKey, data)
```

### 界面操作函数

```javascript
// 显示消息
showMessage(type, message)
// type: 'success', 'warning', 'error', 'info'

// 显示确认对话框
showConfirm(message, callback)

// 显示输入对话框
showPrompt(message, defaultValue, callback)

// 页面跳转
navigateTo(path)

// 刷新页面
refreshPage()
```

### API调用函数

```javascript
// 调用API
callApi(url, method, data, options)

// GET请求
get(url, params)

// POST请求
post(url, data)

// PUT请求
put(url, data)

// DELETE请求
delete(url, params)
```

## 📝 最佳实践

### 1. 命名规范

- **组件ID**: 使用kebab-case，如`user-name-input`
- **变量名**: 使用camelCase，如`currentUserId`
- **数据键**: 使用camelCase，如`userList`

### 2. 错误处理

```javascript
try {
  const result = await callApi('/api/data', 'GET');
  // 处理成功结果
} catch (error) {
  console.error('API调用失败:', error);
  showMessage('error', '数据加载失败，请重试');
}
```

### 3. 性能优化

- 避免在`input`事件中频繁调用API
- 使用防抖处理实时搜索
- 合理使用变量缓存数据

### 4. 数据验证

```javascript
// 输入验证
if (!value || value.trim() === '') {
  showMessage('error', '输入不能为空');
  return false;
}

// 格式验证
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
if (!emailRegex.test(email)) {
  showMessage('error', '邮箱格式不正确');
  return false;
}
```

## 🎨 界面操作示例

### 示例3：动态表单验证

**场景**：创建一个注册表单，包含用户名、邮箱、密码字段，实现实时验证和提交

#### 用户名输入框配置

**组件ID**: `username-input`

**事件配置**：
```json
[
  {
    "type": "blur",
    "action": {
      "type": "javascript",
      "jsCode": "const username = event.target.value;\nif (!username || username.length < 3) {\n  setComponentValue('username-error', '用户名至少3个字符');\n  showComponent('username-error');\n} else {\n  hideComponent('username-error');\n  // 检查用户名是否已存在\n  callApi('/api/user/check', 'POST', {username: username}).then(result => {\n    if (result.exists) {\n      setComponentValue('username-error', '用户名已存在');\n      showComponent('username-error');\n    }\n  });\n}"
    }
  }
]
```

#### 邮箱输入框配置

**组件ID**: `email-input`

**事件配置**：
```json
[
  {
    "type": "blur",
    "action": {
      "type": "javascript",
      "jsCode": "const email = event.target.value;\nconst emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n\nif (!email) {\n  setComponentValue('email-error', '邮箱不能为空');\n  showComponent('email-error');\n} else if (!emailRegex.test(email)) {\n  setComponentValue('email-error', '邮箱格式不正确');\n  showComponent('email-error');\n} else {\n  hideComponent('email-error');\n}"
    }
  }
]
```

#### 提交按钮配置

**组件ID**: `submit-button`

**事件配置**：
```json
[
  {
    "type": "click",
    "action": {
      "type": "javascript",
      "jsCode": "// 获取表单数据\nconst username = getComponentValue('username-input');\nconst email = getComponentValue('email-input');\nconst password = getComponentValue('password-input');\n\n// 验证所有字段\nlet hasError = false;\n\nif (!username || username.length < 3) {\n  setComponentValue('username-error', '用户名至少3个字符');\n  showComponent('username-error');\n  hasError = true;\n}\n\nif (!email || !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n  setComponentValue('email-error', '请输入有效的邮箱地址');\n  showComponent('email-error');\n  hasError = true;\n}\n\nif (!password || password.length < 6) {\n  setComponentValue('password-error', '密码至少6个字符');\n  showComponent('password-error');\n  hasError = true;\n}\n\nif (hasError) {\n  showMessage('error', '请修正表单错误后重试');\n  return;\n}\n\n// 提交数据\ndisableComponent('submit-button');\nsetComponentValue('submit-button', '提交中...');\n\ncallApi('/api/user/register', 'POST', {\n  username: username,\n  email: email,\n  password: password\n}).then(result => {\n  if (result.success) {\n    showMessage('success', '注册成功');\n    navigateTo('/login');\n  } else {\n    showMessage('error', result.message || '注册失败');\n  }\n}).catch(error => {\n  showMessage('error', '网络错误，请重试');\n}).finally(() => {\n  enableComponent('submit-button');\n  setComponentValue('submit-button', '提交');\n});"
    }
  }
]
```

### 示例4：数据列表操作

**场景**：用户列表页面，包含搜索、分页、删除等功能

#### 搜索框配置

**组件ID**: `search-input`

**事件配置**：
```json
[
  {
    "type": "input",
    "action": {
      "type": "javascript",
      "jsCode": "// 防抖处理\nclearTimeout(window.searchTimer);\nwindow.searchTimer = setTimeout(() => {\n  const keyword = event.target.value;\n  setVariable('searchKeyword', keyword);\n  setVariable('currentPage', 1);\n  loadUserList();\n}, 500);\n\n// 加载用户列表函数\nfunction loadUserList() {\n  const keyword = getVariable('searchKeyword') || '';\n  const page = getVariable('currentPage') || 1;\n  \n  callApi('/api/users', 'GET', {\n    keyword: keyword,\n    page: page,\n    size: 10\n  }).then(result => {\n    setComponentData('user-table', result.data.records);\n    setComponentValue('total-count', result.data.total);\n    setComponentData('pagination', {\n      current: result.data.current,\n      total: result.data.total,\n      pageSize: result.data.size\n    });\n  }).catch(error => {\n    showMessage('error', '加载用户列表失败');\n  });\n}"
    }
  }
]
```

#### 删除按钮配置

**组件ID**: `delete-button`

**事件配置**：
```json
[
  {
    "type": "click",
    "action": {
      "type": "javascript",
      "jsCode": "const userId = getVariable('selectedUserId');\n\nif (!userId) {\n  showMessage('warning', '请选择要删除的用户');\n  return;\n}\n\nshowConfirm('确定要删除这个用户吗？', (confirmed) => {\n  if (confirmed) {\n    callApi(`/api/users/${userId}`, 'DELETE').then(result => {\n      if (result.success) {\n        showMessage('success', '删除成功');\n        loadUserList(); // 重新加载列表\n      } else {\n        showMessage('error', result.message || '删除失败');\n      }\n    }).catch(error => {\n      showMessage('error', '删除失败，请重试');\n    });\n  }\n});"
    }
  }
]
```

## 🔧 高级技巧

### 1. 组件间通信

```javascript
// 方式1：通过变量传递数据
setVariable('selectedUser', userData);
const selectedUser = getVariable('selectedUser');

// 方式2：通过事件传递数据
// 在组件A中触发自定义事件
triggerEvent('userSelected', userData);

// 在组件B中监听自定义事件
addEventListener('userSelected', (data) => {
  setComponentData('user-detail', data);
});
```

### 2. 条件逻辑处理

```javascript
// 根据用户角色显示不同内容
const userRole = getVariable('userRole');

if (userRole === 'admin') {
  showComponent('admin-panel');
  hideComponent('user-panel');
} else if (userRole === 'user') {
  hideComponent('admin-panel');
  showComponent('user-panel');
} else {
  hideComponent('admin-panel');
  hideComponent('user-panel');
  showComponent('login-panel');
}
```

### 3. 数据格式化

```javascript
// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

// 格式化金额
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount);
};

// 应用格式化
const orders = getApiData('orderList');
const formattedOrders = orders.map(order => ({
  ...order,
  createTime: formatDate(order.createTime),
  amount: formatCurrency(order.amount)
}));

setComponentData('order-table', formattedOrders);
```

### 4. 表单数据收集

```javascript
// 收集表单数据的通用函数
function collectFormData(formComponentIds) {
  const formData = {};

  formComponentIds.forEach(id => {
    const value = getComponentValue(id);
    const fieldName = id.replace('-input', '').replace('-select', '');
    formData[fieldName] = value;
  });

  return formData;
}

// 使用示例
const formData = collectFormData([
  'username-input',
  'email-input',
  'phone-input',
  'department-select'
]);

console.log('表单数据:', formData);
```

## 🚨 常见问题解决

### 1. 数据引用失效

**问题**：使用`{{components.componentId.value}}`无法获取到值

**解决方案**：
```javascript
// 错误写法
"{{components.username-input.value}}"

// 正确写法
const username = getComponentValue('username-input');
```

### 2. API调用失败

**问题**：API调用返回错误或无响应

**解决方案**：
```javascript
try {
  const result = await callApi('/api/data', 'GET');
  // 检查响应格式
  if (result && result.success) {
    // 处理成功数据
  } else {
    throw new Error(result.message || '请求失败');
  }
} catch (error) {
  console.error('API错误:', error);
  showMessage('error', '数据加载失败');
}
```

### 3. 组件状态不更新

**问题**：调用`setComponentValue`后组件显示没有变化

**解决方案**：
```javascript
// 确保组件ID正确
setComponentValue('correct-component-id', newValue);

// 强制刷新组件
refreshComponent('component-id');

// 或者使用延迟更新
setTimeout(() => {
  setComponentValue('component-id', newValue);
}, 100);
```

## 🔗 相关文档

- [输入框组件文档](./input-component.md)
- [按钮组件文档](./button-component.md)
- [API接口规范](./api-specification.md)
- [组件开发指南](./component-development.md)
- [数据绑定详解](./data-binding-guide.md)
- [JavaScript API参考](./javascript-api-reference.md)
