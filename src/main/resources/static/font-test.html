<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字体文件访问测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #results {
            margin-top: 20px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            background-color: #f8f9fa;
            border-left: 3px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>字体文件访问测试</h1>
    
    <div class="test-section info">
        <h3>测试说明</h3>
        <p>此页面用于测试字体文件的CORS访问和加载功能。</p>
        <p>请确保后端服务器正在运行在 http://localhost:8080</p>
    </div>

    <div class="test-section">
        <h3>测试控制</h3>
        <button onclick="testFontFileAccess()">测试字体文件访问</button>
        <button onclick="testCorsHeaders()">测试CORS头部</button>
        <button onclick="testFontLoading()">测试字体加载</button>
        <button onclick="clearResults()">清除结果</button>
    </div>

    <div id="results"></div>

    <script>
        const resultsDiv = document.getElementById('results');

        function log(message, type = 'info') {
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsDiv.appendChild(entry);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        async function testFontFileAccess() {
            log('开始测试字体文件访问...', 'info');
            
            // 测试一个示例字体文件
            const testFileName = 'font_20250602032451_bc753e43.TTF';
            const fontUrl = `http://localhost:8080/api/fonts/file/${testFileName}`;
            
            try {
                const response = await fetch(fontUrl, {
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    log(`✅ 字体文件访问成功: ${response.status} ${response.statusText}`, 'success');
                    log(`Content-Type: ${response.headers.get('Content-Type')}`, 'info');
                    log(`Content-Length: ${response.headers.get('Content-Length')}`, 'info');
                } else {
                    log(`❌ 字体文件访问失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`❌ 字体文件访问异常: ${error.message}`, 'error');
            }
        }

        async function testCorsHeaders() {
            log('开始测试CORS头部...', 'info');
            
            const testFileName = 'font_20250602032451_bc753e43.TTF';
            const fontUrl = `http://localhost:8080/api/fonts/file/${testFileName}`;
            
            try {
                // 先发送OPTIONS请求
                const optionsResponse = await fetch(fontUrl, {
                    method: 'OPTIONS',
                    mode: 'cors'
                });
                
                log(`OPTIONS请求状态: ${optionsResponse.status}`, 'info');
                log(`Access-Control-Allow-Origin: ${optionsResponse.headers.get('Access-Control-Allow-Origin')}`, 'info');
                log(`Access-Control-Allow-Methods: ${optionsResponse.headers.get('Access-Control-Allow-Methods')}`, 'info');
                log(`Access-Control-Allow-Headers: ${optionsResponse.headers.get('Access-Control-Allow-Headers')}`, 'info');
                
                // 再发送GET请求
                const getResponse = await fetch(fontUrl, {
                    method: 'GET',
                    mode: 'cors'
                });
                
                log(`GET请求状态: ${getResponse.status}`, 'info');
                log(`Access-Control-Allow-Origin: ${getResponse.headers.get('Access-Control-Allow-Origin')}`, 'info');
                
            } catch (error) {
                log(`❌ CORS测试异常: ${error.message}`, 'error');
            }
        }

        async function testFontLoading() {
            log('开始测试字体加载...', 'info');
            
            const testFileName = 'font_20250602032451_bc753e43.TTF';
            const fontUrl = `http://localhost:8080/api/fonts/file/${testFileName}`;
            
            try {
                // 检查FontFace API支持
                if ('FontFace' in window) {
                    log('✅ 浏览器支持FontFace API', 'success');
                    
                    const fontFace = new FontFace('TestFont', `url(${fontUrl})`);
                    
                    fontFace.load().then(() => {
                        log('✅ 字体加载成功', 'success');
                        document.fonts.add(fontFace);
                        
                        // 创建测试文本
                        const testDiv = document.createElement('div');
                        testDiv.style.fontFamily = 'TestFont, Arial, sans-serif';
                        testDiv.style.fontSize = '24px';
                        testDiv.style.margin = '10px 0';
                        testDiv.textContent = '字体加载测试文本 - Font Loading Test';
                        resultsDiv.appendChild(testDiv);
                        
                    }).catch(error => {
                        log(`❌ 字体加载失败: ${error.message}`, 'error');
                    });
                    
                } else {
                    log('❌ 浏览器不支持FontFace API', 'error');
                }
                
            } catch (error) {
                log(`❌ 字体加载测试异常: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动检查基本信息
        window.onload = function() {
            log('页面加载完成，开始基本检查...', 'info');
            log(`当前页面URL: ${window.location.href}`, 'info');
            log(`FontFace API支持: ${'FontFace' in window ? '是' : '否'}`, 'info');
            log(`document.fonts支持: ${'fonts' in document ? '是' : '否'}`, 'info');
        };
    </script>
</body>
</html>
