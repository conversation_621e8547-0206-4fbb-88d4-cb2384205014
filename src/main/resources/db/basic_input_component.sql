-- 添加基础输入框组件
INSERT INTO `component` (`name`, `type`, `category`, `icon`, `props`, `styles`, `is_system`, `enabled`, `creator_id`, `create_time`, `update_time`)
SELECT '输入框', 'input', 'form', 'Edit', 
'{"placeholder":"请输入内容","defaultValue":"","maxLength":0,"type":"text","clearable":true,"showPassword":false,"disabled":false,"readonly":false,"prefixIcon":"","suffixIcon":"","dataKey":"","validation":null}', 
'{"width":"100%","margin":"0 0 15px 0"}', 
1, 1, 1, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM `component` WHERE `type` = 'input');

-- 更新现有的输入框组件（如果存在但属性不完整）
UPDATE `component` 
SET `props` = '{"placeholder":"请输入内容","defaultValue":"","maxLength":0,"type":"text","clearable":true,"showPassword":false,"disabled":false,"readonly":false,"prefixIcon":"","suffixIcon":"","dataKey":"","validation":null}',
    `styles` = '{"width":"100%","margin":"0 0 15px 0"}',
    `name` = '输入框',
    `category` = 'form',
    `icon` = 'Edit',
    `is_system` = 1,
    `enabled` = 1,
    `update_time` = NOW()
WHERE `type` = 'input' AND (`props` IS NULL OR `props` = '' OR JSON_LENGTH(`props`) < 5);
