package com.web.lowcode.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;

/**
 * 基础输入框组件初始化服务
 * 
 * <AUTHOR>
 * @since 2024-01-20
 */
@Service
public class BasicInputComponentInitService {

    private static final Logger logger = LoggerFactory.getLogger(BasicInputComponentInitService.class);

    @Autowired
    private DataSource dataSource;

    /**
     * 初始化基础输入框组件数据
     */
    @PostConstruct
    public void initBasicInputComponent() {
        try {
            logger.info("Initializing basic input component...");

            // 执行SQL脚本初始化基础输入框组件数据
            ResourceDatabasePopulator populator = new ResourceDatabasePopulator();
            populator.addScript(new ClassPathResource("db/basic_input_component.sql"));
            populator.execute(dataSource);

            logger.info("Basic input component initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize basic input component", e);
        }
    }
}
