package com.web.lowcode.generator.component.form;

import com.web.lowcode.generator.component.AbstractComponentHandler;
import com.web.lowcode.generator.component.ComponentTemplateBuilder;
import com.web.lowcode.generator.component.constants.ComponentConstants.FormProps;
import com.web.lowcode.generator.component.constants.ComponentConstants.ComponentTypes;
import com.web.lowcode.generator.component.constants.ComponentConstants.Events;
import com.web.lowcode.generator.component.utils.ComponentPropsUtil;
import org.json.JSONObject;

/**
 * 基础输入框组件处理器
 * 
 * <AUTHOR>
 * @since 2024-01-20
 */
public class InputComponentHandler extends AbstractComponentHandler {
    
    @Override
    public void handleProps(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 调用父类方法处理基本属性
        super.handleProps(component, templateBuilder);

        // 处理输入框特有属性
        JSONObject props = ComponentPropsUtil.getProps(component);
        if (!props.isEmpty()) {
            // 处理输入框类型
            if (props.has(FormProps.TYPE)) {
                String inputType = props.optString(FormProps.TYPE, "text");
                templateBuilder.addProp("type", inputType);
            }

            // 处理最大长度
            if (props.has(FormProps.MAX_LENGTH)) {
                int maxLength = props.optInt(FormProps.MAX_LENGTH, 0);
                if (maxLength > 0) {
                    templateBuilder.addProp(":maxlength", String.valueOf(maxLength));
                    templateBuilder.addProp("show-word-limit", "true");
                }
            }

            // 处理清除按钮
            if (props.has(FormProps.CLEARABLE)) {
                templateBuilder.addProp(":clearable", props.optString(FormProps.CLEARABLE));
            }

            // 处理密码显示切换
            if (props.has(FormProps.SHOW_PASSWORD)) {
                templateBuilder.addProp(":show-password", props.optString(FormProps.SHOW_PASSWORD));
            }

            // 处理前置图标
            if (props.has(FormProps.PREFIX_ICON) && !props.optString(FormProps.PREFIX_ICON).isEmpty()) {
                String prefixIcon = props.optString(FormProps.PREFIX_ICON);
                templateBuilder.setHasChildren(true);
                templateBuilder.addSlot("prefix", "<el-icon><" + prefixIcon + " /></el-icon>");
            }

            // 处理后置图标
            if (props.has(FormProps.SUFFIX_ICON) && !props.optString(FormProps.SUFFIX_ICON).isEmpty()) {
                String suffixIcon = props.optString(FormProps.SUFFIX_ICON);
                templateBuilder.setHasChildren(true);
                templateBuilder.addSlot("suffix", "<el-icon><" + suffixIcon + " /></el-icon>");
            }

            // 处理只读状态
            if (props.has(FormProps.READONLY)) {
                templateBuilder.addProp(":readonly", props.optString(FormProps.READONLY));
            }

            // 处理数据键
            if (props.has(FormProps.DATA_KEY) && !props.optString(FormProps.DATA_KEY).isEmpty()) {
                String dataKey = props.optString(FormProps.DATA_KEY);
                templateBuilder.addProp("data-key", dataKey);
            }

            // 处理验证规则
            if (props.has(FormProps.VALIDATION)) {
                JSONObject validation = props.optJSONObject(FormProps.VALIDATION);
                if (validation != null && validation.has("rules")) {
                    templateBuilder.addProp(":rules", validation.toString());
                }
            }
        }
    }

    @Override
    public void handleEvents(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 调用父类方法处理基本事件
        super.handleEvents(component, templateBuilder);

        // 处理输入框特有事件
        JSONObject events = ComponentPropsUtil.getEvents(component);
        if (!events.isEmpty()) {
            // 处理输入事件
            if (events.has(Events.INPUT)) {
                templateBuilder.addEvent("@input", events.optString(Events.INPUT));
            }

            // 处理变化事件
            if (events.has(Events.CHANGE)) {
                templateBuilder.addEvent("@change", events.optString(Events.CHANGE));
            }

            // 处理焦点事件
            if (events.has(Events.FOCUS)) {
                templateBuilder.addEvent("@focus", events.optString(Events.FOCUS));
            }

            // 处理失焦事件
            if (events.has(Events.BLUR)) {
                templateBuilder.addEvent("@blur", events.optString(Events.BLUR));
            }

            // 处理清除事件
            if (events.has(Events.CLEAR)) {
                templateBuilder.addEvent("@clear", events.optString(Events.CLEAR));
            }

            // 处理键盘事件
            if (events.has(Events.KEYDOWN)) {
                templateBuilder.addEvent("@keydown", events.optString(Events.KEYDOWN));
            }

            if (events.has(Events.KEYUP)) {
                templateBuilder.addEvent("@keyup", events.optString(Events.KEYUP));
            }

            if (events.has(Events.KEYPRESS)) {
                templateBuilder.addEvent("@keypress", events.optString(Events.KEYPRESS));
            }
        }
    }

    @Override
    public void handleContent(JSONObject component, ComponentTemplateBuilder templateBuilder) {
        // 输入框组件没有内容，但可能有插槽内容
        // 插槽内容在handleProps中已经处理
    }

    @Override
    public String getComponentType() {
        return ComponentTypes.INPUT;
    }
}
