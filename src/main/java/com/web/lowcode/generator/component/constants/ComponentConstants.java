package com.web.lowcode.generator.component.constants;

/**
 * 组件常量定义
 * 集中管理组件的属性名称、事件名称等常量
 */
public class ComponentConstants {
    
    /**
     * 通用属性名称
     */
    public static class CommonProps {
        // 基础属性
        public static final String ID = "id";
        public static final String TYPE = "type";
        public static final String CLASS = "class";
        public static final String STYLE = "style";
        public static final String TITLE = "title";
        public static final String DISABLED = "disabled";
        
        // 尺寸相关
        public static final String WIDTH = "width";
        public static final String HEIGHT = "height";
        public static final String MIN_WIDTH = "minWidth";
        public static final String MIN_HEIGHT = "minHeight";
        public static final String MAX_WIDTH = "maxWidth";
        public static final String MAX_HEIGHT = "maxHeight";
        
        // 边距相关
        public static final String MARGIN = "margin";
        public static final String PADDING = "padding";
        
        // 字体相关
        public static final String FONT_SIZE = "fontSize";
        public static final String FONT_WEIGHT = "fontWeight";
        public static final String FONT_FAMILY = "fontFamily";
        public static final String COLOR = "color";
        
        // 背景相关
        public static final String BACKGROUND_COLOR = "backgroundColor";
        public static final String BACKGROUND_IMAGE = "backgroundImage";
        
        // 边框相关
        public static final String BORDER = "border";
        public static final String BORDER_RADIUS = "borderRadius";
        
        // 定位相关
        public static final String POSITION = "position";
        public static final String TOP = "top";
        public static final String RIGHT = "right";
        public static final String BOTTOM = "bottom";
        public static final String LEFT = "left";
        public static final String Z_INDEX = "zIndex";
        
        // 显示相关
        public static final String DISPLAY = "display";
        public static final String VISIBILITY = "visibility";
        public static final String OPACITY = "opacity";
        
        // 对齐相关
        public static final String TEXT_ALIGN = "textAlign";
        public static final String VERTICAL_ALIGN = "verticalAlign";
        
        // 内容相关
        public static final String CONTENT = "content";
        public static final String TEXT = "text";
        public static final String HTML = "html";
        
        // 子元素相关
        public static final String CHILDREN = "children";
        public static final String ELEMENTS = "elements";
    }
    
    /**
     * 表单组件属性名称
     */
    public static class FormProps {
        // 通用表单属性
        public static final String VALUE = "value";
        public static final String DEFAULT_VALUE = "defaultValue";
        public static final String PLACEHOLDER = "placeholder";
        public static final String CLEARABLE = "clearable";
        public static final String READONLY = "readonly";
        public static final String DISABLED = "disabled";
        public static final String SIZE = "size";
        public static final String MAXLENGTH = "maxlength";
        public static final String SHOW_WORD_LIMIT = "showWordLimit";
        
        // 数字输入框属性
        public static final String MIN = "min";
        public static final String MAX = "max";
        public static final String STEP = "step";
        public static final String PRECISION = "precision";
        public static final String STEP_STRICTLY = "stepStrictly";
        public static final String CONTROLS = "controls";
        
        // 文本区域属性
        public static final String ROWS = "rows";
        public static final String AUTOSIZE = "autosize";
        public static final String MIN_ROWS = "minRows";
        public static final String MAX_ROWS = "maxRows";
        
        // 密码输入框属性
        public static final String SHOW_PASSWORD = "showPassword";
        
        // 搜索输入框属性
        public static final String SHOW_PREFIX = "showPrefix";
        public static final String SHOW_BUTTON = "showButton";
        public static final String BUTTON_TYPE = "buttonType";
        public static final String BUTTON_TEXT = "buttonText";

        // 基础输入框属性
        public static final String TYPE = "type";
        public static final String MAX_LENGTH = "maxLength";
        public static final String PREFIX_ICON = "prefixIcon";
        public static final String SUFFIX_ICON = "suffixIcon";
        public static final String DATA_KEY = "dataKey";
        public static final String VALIDATION = "validation";
    }
    
    /**
     * 事件名称
     */
    public static class Events {
        // 通用事件
        public static final String CLICK = "click";
        public static final String DBLCLICK = "dblclick";
        public static final String MOUSEENTER = "mouseenter";
        public static final String MOUSELEAVE = "mouseleave";
        public static final String MOUSEDOWN = "mousedown";
        public static final String MOUSEUP = "mouseup";
        public static final String MOUSEMOVE = "mousemove";
        
        // 表单事件
        public static final String INPUT = "input";
        public static final String CHANGE = "change";
        public static final String FOCUS = "focus";
        public static final String BLUR = "blur";
        public static final String CLEAR = "clear";
        public static final String SEARCH = "search";

        // 键盘事件
        public static final String KEYDOWN = "keydown";
        public static final String KEYUP = "keyup";
        public static final String KEYPRESS = "keypress";
        
        // 自定义事件
        public static final String CUSTOM = "custom";
    }
    
    /**
     * 组件类型
     */
    public static class ComponentTypes {
        public static final String DEFAULT = "default";

        // 基础组件
        public static final String TEXT = "text";
        public static final String BUTTON = "button";
        public static final String IMAGE = "image";
        public static final String CONTAINER = "container";
        public static final String ALERT = "alert";
        
        // 表单组件
        public static final String INPUT = "input";
        public static final String TEXTAREA = "textarea";
        public static final String NUMBER_INPUT = "number-input";
        public static final String PASSWORD_INPUT = "password-input";
        public static final String SEARCH_INPUT = "search-input";
        public static final String RADIO = "radio";
        public static final String CHECKBOX = "checkbox";
        public static final String SELECT = "select";
        public static final String SWITCH = "switch";
        public static final String SLIDER = "slider";
        
        // 布局组件
        public static final String ROW = "row";
        public static final String COL = "col";
        public static final String GRID_CONTAINER = "grid-container";
        public static final String CARD_CONTAINER = "card-container";
        public static final String TAB_CONTAINER = "tab-container";
        public static final String COLLAPSE_CONTAINER = "collapse-container";
        
        // 数据组件
        public static final String STATISTIC = "statistic";
        public static final String LINE_CHART = "line-chart";
        public static final String BAR_CHART = "bar-chart";
        public static final String PIE_CHART = "pie-chart";
        
        // 流程组件
        public static final String FLOW_NODE = "flowNode";
        public static final String FLOW_CONNECTION = "flowConnection";
        public static final String FLOW_START = "flowStart";
        public static final String FLOW_END = "flowEnd";
        public static final String FLOW_DECISION = "flowDecision";
        public static final String FLOW_PROCESS = "flowProcess";
        
        // 自定义组件
        public static final String CUSTOM = "custom";
    }
    
    /**
     * JSON属性名称
     */
    public static class JsonProps {
        public static final String PROPS = "props";
        public static final String STYLES = "styles";
        public static final String EVENTS = "events";
        public static final String CHILDREN = "children";
        public static final String TYPE = "type";
        public static final String ID = "id";
        public static final String TEXT = "text";
    }
}
