# 字体数据显示不匹配问题分析

## 🚨 问题描述

用户反馈：字体管理页面接口只返回16条数据，但是页面显示出了30条。

## 🔍 问题分析

### 可能的原因

1. **前端数据合并逻辑**：
   - 系统预定义字体（14个）+ 服务器返回字体（16个）= 30个
   - 前端store中有预定义的系统字体和Web字体

2. **后端分页设置**：
   - 默认分页大小可能设置为16
   - 实际数据库中可能有更多字体

3. **接口调用错误**：
   - 前端调用的是`getEnabledFonts()`而不是分页接口
   - 可能需要调用`getFontPage()`接口

## 📊 数据来源分析

### 前端预定义字体（store/index.js）

**系统字体（10个）**：
- system-default, arial, helvetica, times, georgia, courier, verdana
- pingfang, heiti, songti, kaiti

**Web字体（3个）**：
- roboto, open-sans, lato

**总计**：13个预定义字体

### 后端返回字体

**接口**：`/api/fonts/enabled`
**预期**：返回数据库中所有启用的字体

## 🛠️ 解决方案

### 方案1：修改前端调用分页接口

```javascript
// 修改 FontManager.vue 中的 loadFontsFromServer 方法
const loadFontsFromServer = async () => {
  try {
    console.log('开始从服务器加载字体列表...')

    // 使用分页接口获取所有字体
    const result = await fontApi.getFontPage({
      current: 1,
      size: 1000, // 设置足够大的页面大小
      enabled: 1   // 只获取启用的字体
    })
    
    console.log('服务器返回的字体数据:', result)

    if (result && result.data && result.data.records) {
      const fonts = result.data.records
      console.log('解析到的字体列表:', fonts.length, '个字体')
      
      // 更新store中的字体库
      editorStore.setFontLibrary(fonts)
      
      ElMessage.success(`成功加载 ${fonts.length} 个字体`)
    }
  } catch (error) {
    console.error('从服务器加载字体列表失败:', error)
    ElMessage.error('加载字体列表失败')
  }
}
```

### 方案2：修改后端getEnabledFonts接口

```java
// 在FontServiceImpl中修改getEnabledFonts方法
@Override
public List<Font> getEnabledFonts() {
    LambdaQueryWrapper<Font> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(Font::getEnabled, 1)
           .eq(Font::getDeleted, 0)
           .orderBy(true, true, Font::getSortOrder)
           .orderBy(false, true, Font::getCreateTime);
    
    List<Font> fonts = list(wrapper);
    log.info("查询到启用字体数量: {}", fonts.size());
    
    return fonts;
}
```

### 方案3：添加调试信息

```javascript
// 在FontManager.vue中添加详细的调试信息
const loadFontsFromServer = async () => {
  try {
    console.log('=== 字体加载调试信息 ===')
    console.log('当前store中的字体数量:', editorStore.fontLibrary.length)
    console.log('系统字体:', editorStore.fontLibrary.filter(f => f.type === 'system').length)
    console.log('Web字体:', editorStore.fontLibrary.filter(f => f.type === 'web').length)
    console.log('自定义字体:', editorStore.fontLibrary.filter(f => f.type === 'custom').length)

    const result = await fontApi.getEnabledFonts()
    console.log('API返回原始数据:', result)
    
    if (result && result.data && Array.isArray(result.data)) {
      const fonts = result.data
      console.log('服务器返回字体数量:', fonts.length)
      console.log('服务器字体详情:', fonts.map(f => ({ id: f.id, name: f.name, type: f.type })))
      
      editorStore.setFontLibrary(fonts)
      
      console.log('合并后字体数量:', editorStore.fontLibrary.length)
      console.log('=== 调试信息结束 ===')
    }
  } catch (error) {
    console.error('字体加载失败:', error)
  }
}
```

## 🧪 验证步骤

### 步骤1：检查后端数据

```sql
-- 查询数据库中的字体数据
SELECT COUNT(*) as total_fonts FROM font WHERE deleted = 0;
SELECT COUNT(*) as enabled_fonts FROM font WHERE deleted = 0 AND enabled = 1;
SELECT type, COUNT(*) as count FROM font WHERE deleted = 0 AND enabled = 1 GROUP BY type;
```

### 步骤2：检查API响应

```bash
# 测试启用字体接口
curl "http://localhost:8080/api/fonts/enabled"

# 测试分页接口
curl "http://localhost:8080/api/fonts/page?current=1&size=100&enabled=1"
```

### 步骤3：检查前端日志

1. 打开浏览器开发者工具
2. 切换到Console标签
3. 刷新字体管理页面
4. 查看详细的调试信息

## 📋 预期结果

修复后应该看到：

1. **后端日志**：
   ```
   查询到启用字体数量: X
   ```

2. **前端日志**：
   ```
   服务器返回字体数量: X
   合并后字体数量: Y (X + 预定义字体数量)
   ```

3. **页面显示**：
   - 表格显示的字体数量与实际数据一致
   - 分页信息正确显示

## 🔧 立即行动

1. **添加setFontLibrary方法**（已完成）
2. **添加调试信息到FontManager.vue**
3. **检查后端数据库字体数量**
4. **验证API响应格式**
5. **根据调试结果选择最佳解决方案**
