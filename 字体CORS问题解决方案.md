# 字体CORS问题解决方案

## 🚨 问题描述

用户在访问字体文件时遇到以下错误：
```
Access to fetch at 'http://localhost:8080/api/fonts/file/font_20250602032451_bc753e43.TTF' 
from origin 'http://localhost:3000' has been blocked by CORS policy: 
No 'Access-Control-Allow-Origin' header is present on the requested resource.

HEAD http://localhost:8080/api/fonts/file/font_20250602032451_bc753e43.TTF net::ERR_FAILED 403 (Forbidden)
```

## 🔍 问题分析

1. **CORS策略阻止**：跨域资源共享配置不正确
2. **403 Forbidden**：权限配置问题
3. **OPTIONS预检请求失败**：缺少OPTIONS请求处理
4. **多重CORS配置冲突**：存在多个CORS配置可能冲突

## ✅ 解决方案

### 1. 修改SecurityConfig.java

**增强字体接口权限配置**：
```java
// 字体相关接口 - 允许所有HTTP方法包括OPTIONS
.requestMatchers(HttpMethod.GET, "/api/fonts/**").permitAll()
.requestMatchers(HttpMethod.POST, "/api/fonts/**").permitAll()
.requestMatchers(HttpMethod.PUT, "/api/fonts/**").permitAll()
.requestMatchers(HttpMethod.DELETE, "/api/fonts/**").permitAll()
.requestMatchers(HttpMethod.OPTIONS, "/api/fonts/**").permitAll()
.requestMatchers(HttpMethod.HEAD, "/api/fonts/**").permitAll()
```

**优化CORS配置**：
```java
@Bean
public CorsConfigurationSource corsConfigurationSource() {
    CorsConfiguration configuration = new CorsConfiguration();
    configuration.setAllowedOriginPatterns(Arrays.asList("*"));
    configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"));
    configuration.setAllowedHeaders(Arrays.asList("*"));
    configuration.setExposedHeaders(Arrays.asList("*"));
    configuration.setAllowCredentials(true);
    configuration.setMaxAge(3600L);
    
    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    source.registerCorsConfiguration("/**", configuration);
    return source;
}
```

### 2. 增强FontController.java

**添加OPTIONS请求处理**：
```java
@RequestMapping(value = "/file/{fileName}", method = RequestMethod.OPTIONS)
@Operation(summary = "字体文件预检请求", description = "处理CORS预检请求")
public ResponseEntity<Void> fontFileOptions(@PathVariable("fileName") String fileName) {
    HttpHeaders headers = new HttpHeaders();
    headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
    headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS, HEAD");
    headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*");
    headers.add(HttpHeaders.ACCESS_CONTROL_MAX_AGE, "3600");
    
    return ResponseEntity.ok().headers(headers).build();
}
```

**增强响应头设置**：
```java
// 添加CORS支持 - 增强版本
headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS, HEAD");
headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*");
headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "*");
headers.add(HttpHeaders.ACCESS_CONTROL_MAX_AGE, "3600");
headers.add(HttpHeaders.CACHE_CONTROL, "public, max-age=31536000");
```

### 3. 创建专用CORS配置

**新建FontCorsConfig.java**：
```java
@Configuration
public class FontCorsConfig implements WebMvcConfigurer {
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // 专门为字体文件配置CORS
        registry.addMapping("/api/fonts/file/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD")
                .allowedHeaders("*")
                .exposedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}
```

### 4. 删除冲突配置

**删除CorsConfig.java**：
- 移除了可能与SecurityConfig冲突的CORS配置

## 🧪 测试验证

### 1. 使用测试页面

访问测试页面：`http://localhost:8080/font-test.html`

### 2. 手动测试

**测试OPTIONS请求**：
```bash
curl -X OPTIONS \
  -H "Origin: http://localhost:3000" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: Content-Type" \
  http://localhost:8080/api/fonts/file/font_20250602032451_bc753e43.TTF
```

**测试GET请求**：
```bash
curl -X GET \
  -H "Origin: http://localhost:3000" \
  http://localhost:8080/api/fonts/file/font_20250602032451_bc753e43.TTF
```

### 3. 浏览器开发者工具

1. 打开Network标签
2. 尝试加载字体
3. 检查请求状态和响应头

## 📋 验证清单

- [ ] 重启Spring Boot应用
- [ ] 确认字体文件存在于uploads/font目录
- [ ] 测试OPTIONS预检请求返回200
- [ ] 测试GET请求返回200且包含正确的CORS头
- [ ] 前端字体加载成功
- [ ] 浏览器控制台无CORS错误

## 🎯 预期结果

修复后应该看到：

1. **OPTIONS请求成功**：
   ```
   Status: 200 OK
   Access-Control-Allow-Origin: *
   Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS, HEAD
   ```

2. **GET请求成功**：
   ```
   Status: 200 OK
   Content-Type: font/ttf
   Access-Control-Allow-Origin: *
   ```

3. **前端加载成功**：
   ```javascript
   // 无CORS错误
   // 字体成功加载到document.fonts
   ```

## 🔧 故障排除

如果问题仍然存在：

1. **检查服务器启动日志**：确认无配置错误
2. **验证文件权限**：确保字体文件可读
3. **清除浏览器缓存**：避免缓存的错误响应
4. **检查防火墙设置**：确保端口8080可访问
5. **使用调试API**：`GET /api/fonts/debug/file-system`

## 📝 注意事项

1. **生产环境**：将`allowedOriginPatterns("*")`改为具体域名
2. **安全考虑**：考虑添加文件类型和大小限制
3. **性能优化**：添加适当的缓存头
4. **监控日志**：关注字体访问的错误日志
