# 字体问题快速诊断和解决方案

## 🚨 当前问题状态

根据诊断结果，发现以下问题：
1. **urlAccessibility**: 字体URL访问异常 - `Failed to fetch`
2. **loadStatus**: 字体加载失败 - `A network error occurred`

## 🔍 问题根因分析

从错误信息 `Failed to fetch` 来看，主要问题是：
1. **后端服务器未运行**：Spring Boot应用可能没有启动
2. **端口8080不可访问**：服务器端口被占用或防火墙阻止
3. **CORS配置未生效**：服务器重启后配置未加载

## ✅ 立即解决方案

### 步骤1: 确认后端服务器状态

**检查服务器是否运行**：
```bash
# 检查端口8080是否被占用
netstat -an | findstr :8080

# 检查Java进程
jps -l | findstr lowcode
```

**如果服务器未运行，启动服务器**：
```bash
# 方法1: 使用Maven
mvn spring-boot:run

# 方法2: 使用IDE
# 在IntelliJ IDEA或Eclipse中运行LowcodeApplication.java

# 方法3: 使用jar包（如果已编译）
java -jar target/lowcode-0.0.1-SNAPSHOT.jar
```

### 步骤2: 验证服务器启动成功

**测试基本接口**：
```bash
# 测试健康检查
curl http://localhost:8080/actuator/health

# 测试字体调试接口
curl http://localhost:8080/api/fonts/debug/file-system
```

**预期响应**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userDir": "F:\\xiangmu\\low-code\\lowCode",
    "fontDirPath": "F:\\xiangmu\\low-code\\lowCode\\uploads\\font",
    "fontDirExists": true,
    "fontDirCanRead": true,
    "files": [...]
  }
}
```

### 步骤3: 测试字体文件直接访问

**使用浏览器测试**：
```
http://localhost:8080/api/fonts/file/font_20250602032451_bc753e43.TTF
```

**使用curl测试**：
```bash
# 测试OPTIONS预检请求
curl -X OPTIONS \
  -H "Origin: http://localhost:3000" \
  -H "Access-Control-Request-Method: GET" \
  http://localhost:8080/api/fonts/file/font_20250602032451_bc753e43.TTF

# 测试GET请求
curl -X GET \
  -H "Origin: http://localhost:3000" \
  http://localhost:8080/api/fonts/file/font_20250602032451_bc753e43.TTF
```

### 步骤4: 验证CORS配置

**检查响应头**：
```bash
curl -I -H "Origin: http://localhost:3000" \
  http://localhost:8080/api/fonts/file/font_20250602032451_bc753e43.TTF
```

**预期响应头**：
```
HTTP/1.1 200 OK
Content-Type: font/ttf
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS, HEAD
Access-Control-Allow-Headers: *
```

## 🛠️ 常见问题解决

### 问题1: 端口8080被占用

**解决方案**：
```bash
# 查找占用端口的进程
netstat -ano | findstr :8080

# 结束占用进程（替换PID）
taskkill /PID <PID> /F

# 或者修改application.properties中的端口
server.port=8081
```

### 问题2: 权限不足

**解决方案**：
```bash
# 以管理员身份运行命令行
# 或者修改uploads目录权限
icacls uploads /grant Everyone:F /T
```

### 问题3: 防火墙阻止

**解决方案**：
```bash
# 临时关闭Windows防火墙进行测试
# 或者添加端口8080到防火墙例外
```

### 问题4: Maven/Java环境问题

**解决方案**：
```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version

# 清理并重新编译
mvn clean install
```

## 🧪 快速验证脚本

创建一个批处理文件进行快速测试：

```batch
@echo off
echo 正在检查后端服务器状态...

echo.
echo 1. 检查端口8080...
netstat -an | findstr :8080

echo.
echo 2. 测试基本连接...
curl -s -o nul -w "HTTP状态码: %%{http_code}\n" http://localhost:8080/api/fonts/debug/file-system

echo.
echo 3. 测试字体文件访问...
curl -s -o nul -w "HTTP状态码: %%{http_code}\n" http://localhost:8080/api/fonts/file/font_20250602032451_bc753e43.TTF

echo.
echo 4. 测试CORS头部...
curl -I -H "Origin: http://localhost:3000" http://localhost:8080/api/fonts/file/font_20250602032451_bc753e43.TTF

pause
```

## 📋 检查清单

在继续之前，请确认：

- [ ] Spring Boot应用正在运行
- [ ] 端口8080可访问
- [ ] 字体文件存在于uploads/font目录
- [ ] 文件权限正确
- [ ] 防火墙不阻止访问
- [ ] CORS配置已生效

## 🎯 下一步行动

1. **立即启动后端服务器**
2. **使用浏览器访问**: `http://localhost:8080/api/fonts/debug/file-system`
3. **确认返回正常的JSON响应**
4. **重新测试前端字体加载功能**

如果服务器启动成功，字体文件访问问题应该会立即解决。
