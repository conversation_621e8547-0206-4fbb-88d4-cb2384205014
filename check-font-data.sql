-- 字体数据检查SQL脚本

-- 1. 检查字体表总数据量
SELECT 
    '总字体数量' as 统计项,
    COUNT(*) as 数量
FROM font 
WHERE deleted = 0;

-- 2. 检查启用字体数量
SELECT 
    '启用字体数量' as 统计项,
    COUNT(*) as 数量
FROM font 
WHERE deleted = 0 AND enabled = 1;

-- 3. 按类型统计字体数量
SELECT 
    type as 字体类型,
    COUNT(*) as 数量,
    SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) as 启用数量,
    SUM(CASE WHEN enabled = 0 THEN 1 ELSE 0 END) as 禁用数量
FROM font 
WHERE deleted = 0 
GROUP BY type
ORDER BY type;

-- 4. 查看最近创建的字体
SELECT 
    id,
    name,
    family,
    type,
    enabled,
    create_time
FROM font 
WHERE deleted = 0 
ORDER BY create_time DESC 
LIMIT 20;

-- 5. 检查是否有重复的字体名称
SELECT 
    name,
    COUNT(*) as 重复次数
FROM font 
WHERE deleted = 0 
GROUP BY name 
HAVING COUNT(*) > 1;

-- 6. 检查是否有重复的字体族
SELECT 
    family,
    COUNT(*) as 重复次数
FROM font 
WHERE deleted = 0 
GROUP BY family 
HAVING COUNT(*) > 1;

-- 7. 查看所有启用的字体详情
SELECT 
    id,
    name,
    family,
    type,
    font_url,
    css_url,
    enabled,
    sort_order,
    create_time
FROM font 
WHERE deleted = 0 AND enabled = 1 
ORDER BY 
    CASE type 
        WHEN 'system' THEN 1
        WHEN 'web' THEN 2
        WHEN 'custom' THEN 3
        ELSE 4
    END,
    sort_order ASC, 
    create_time DESC;

-- 8. 检查字体文件URL格式
SELECT 
    id,
    name,
    type,
    font_url,
    CASE 
        WHEN type = 'custom' AND (font_url IS NULL OR font_url = '') THEN '缺少字体URL'
        WHEN type = 'custom' AND font_url NOT LIKE 'http%' THEN '字体URL格式可能有问题'
        ELSE '正常'
    END as URL状态
FROM font 
WHERE deleted = 0 AND type = 'custom'
ORDER BY create_time DESC;

-- 9. 统计字体文件大小分布
SELECT 
    CASE 
        WHEN file_size IS NULL THEN '未知大小'
        WHEN file_size < 1024*1024 THEN '小于1MB'
        WHEN file_size < 5*1024*1024 THEN '1-5MB'
        WHEN file_size < 10*1024*1024 THEN '5-10MB'
        ELSE '大于10MB'
    END as 文件大小范围,
    COUNT(*) as 数量
FROM font 
WHERE deleted = 0 AND type = 'custom'
GROUP BY 
    CASE 
        WHEN file_size IS NULL THEN '未知大小'
        WHEN file_size < 1024*1024 THEN '小于1MB'
        WHEN file_size < 5*1024*1024 THEN '1-5MB'
        WHEN file_size < 10*1024*1024 THEN '5-10MB'
        ELSE '大于10MB'
    END;

-- 10. 检查最近的字体操作记录（如果有日志表）
-- SELECT * FROM font_usage_log ORDER BY create_time DESC LIMIT 10;
